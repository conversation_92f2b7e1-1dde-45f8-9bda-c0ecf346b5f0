<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProgramUserBook extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_user_books';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'user_id',
        'book_id',
        'start_date',
        'end_date',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program this book assignment belongs to.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the user (student).
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Scope to get completed assignments.
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('end_date');
    }

    /**
     * Scope to get ongoing assignments.
     */
    public function scopeOngoing($query)
    {
        return $query->whereNull('end_date');
    }

    /**
     * Scope to get overdue assignments.
     */
    public function scopeOverdue($query, int $days = 30)
    {
        $overdueDate = now()->subDays($days);
        return $query->whereNull('end_date')
                    ->where('start_date', '<=', $overdueDate);
    }

    /**
     * Scope to get assignments for a specific program.
     */
    public function scopeForProgram($query, int $programId)
    {
        return $query->where('program_id', $programId);
    }

    /**
     * Scope to get assignments for a specific student.
     */
    public function scopeForStudent($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get assignments within date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent assignments.
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('start_date', '>=', now()->subDays($days));
    }

    /**
     * Check if the assignment is completed.
     */
    public function getIsCompletedAttribute(): bool
    {
        return !is_null($this->end_date);
    }

    /**
     * Check if the assignment is ongoing.
     */
    public function getIsOngoingAttribute(): bool
    {
        return is_null($this->end_date);
    }

    /**
     * Check if the assignment is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        if ($this->is_completed) {
            return false;
        }

        // Consider overdue if started more than 30 days ago and not completed
        return $this->start_date->diffInDays(now()) > 30;
    }

    /**
     * Get the duration of the assignment in days.
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->is_completed) {
            return null;
        }

        return $this->start_date->diffInDays($this->end_date);
    }

    /**
     * Get the days since assignment started.
     */
    public function getDaysSinceStartAttribute(): int
    {
        return $this->start_date->diffInDays(now());
    }

    /**
     * Get the assignment status.
     */
    public function getStatusAttribute(): string
    {
        if ($this->is_completed) {
            return 'completed';
        } elseif ($this->is_overdue) {
            return 'overdue';
        } else {
            return 'ongoing';
        }
    }

    /**
     * Get the status color for badges.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'completed' => 'green',
            'overdue' => 'red',
            'ongoing' => 'blue',
            default => 'gray'
        };
    }

    /**
     * Mark the assignment as completed.
     */
    public function markCompleted(?Carbon $completionDate = null): bool
    {
        if ($this->is_completed) {
            return false; // Already completed
        }

        $this->end_date = $completionDate ?? now()->toDateString();
        return $this->save();
    }

    /**
     * Mark the assignment as ongoing (remove completion date).
     */
    public function markOngoing(): bool
    {
        if (!$this->is_completed) {
            return false; // Already ongoing
        }

        $this->end_date = null;
        return $this->save();
    }

    /**
     * Get assignments for a student in a program.
     */
    public static function getStudentAssignments(int $programId, int $userId)
    {
        return static::with(['book', 'program'])
                    ->where('program_id', $programId)
                    ->where('user_id', $userId)
                    ->orderBy('start_date')
                    ->get();
    }

    /**
     * Get completion statistics for a program.
     */
    public static function getProgramStats(int $programId): array
    {
        $total = static::where('program_id', $programId)->count();
        $completed = static::where('program_id', $programId)->completed()->count();
        $ongoing = static::where('program_id', $programId)->ongoing()->count();
        $overdue = static::where('program_id', $programId)->overdue()->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'ongoing' => $ongoing,
            'overdue' => $overdue,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get student reading statistics.
     */
    public static function getStudentStats(int $userId): array
    {
        $total = static::where('user_id', $userId)->count();
        $completed = static::where('user_id', $userId)->completed()->count();
        $ongoing = static::where('user_id', $userId)->ongoing()->count();
        $overdue = static::where('user_id', $userId)->overdue()->count();

        $avgDuration = static::where('user_id', $userId)
                            ->completed()
                            ->get()
                            ->avg(function ($assignment) {
                                return $assignment->duration;
                            });

        return [
            'total' => $total,
            'completed' => $completed,
            'ongoing' => $ongoing,
            'overdue' => $overdue,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'avg_duration' => $avgDuration ? round($avgDuration, 1) : null,
        ];
    }

    /**
     * Boot method to handle automatic date assignment.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($programUserBook) {
            if (is_null($programUserBook->start_date)) {
                $programUserBook->start_date = now()->toDateString();
            }
        });
    }
}
