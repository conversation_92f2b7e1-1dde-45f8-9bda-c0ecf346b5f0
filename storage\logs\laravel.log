[2025-07-07 08:05:37] local.ERROR: Non-static method Illuminate\Foundation\Vite::asset() cannot be called statically {"userId":1,"exception":"[object] (Error(code: 0): Non-static method Illuminate\\Foundation\\Vite::asset() cannot be called statically at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\app\\MoonShine\\Layouts\\MoonShineLayout.php:67)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\src\\AbstractLayout.php(38): App\\MoonShine\\Layouts\\MoonShineLayout->assets()
#1 [internal function]: MoonShine\\UI\\AbstractLayout->__construct()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\DependencyInjection\\MoonShine.php(54): Illuminate\\Foundation\\Application->make()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(279): MoonShine\\Laravel\\DependencyInjection\\MoonShine->getContainer()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(339): MoonShine\\Core\\Pages\\Page->getLayout()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(188): MoonShine\\Core\\Pages\\Page->systemViewData()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(149): MoonShine\\Core\\Pages\\Page->toArray()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(142): MoonShine\\Core\\Pages\\Page->renderView()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(123): MoonShine\\Core\\Pages\\Page->resolveRender()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\HomeController.php(25): MoonShine\\Core\\Pages\\Page->render()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\HomeController->__invoke()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#70 {main}
"} 
[2025-07-07 17:38:41] local.ERROR: Collection should only include [MoonShine\UI\Contracts\FieldsWrapperContract, MoonShine\Contracts\UI\FieldContract, MoonShine\Laravel\Fields\Relationships\ModelRelationField] items, but 'MoonShine\UI\Components\Layout\Box' found at position 4. {"userId":1,"exception":"[object] (UnexpectedValueException(code: 0): Collection should only include [MoonShine\\UI\\Contracts\\FieldsWrapperContract, MoonShine\\Contracts\\UI\\FieldContract, MoonShine\\Laravel\\Fields\\Relationships\\ModelRelationField] items, but 'MoonShine\\UI\\Components\\Layout\\Box' found at position 4. at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php:373)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Support\\Collection->Illuminate\\Support\\Traits\\{closure}()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(364): Illuminate\\Support\\Collection->each()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Resource\\ResourceWithFields.php(93): Illuminate\\Support\\Collection->ensure()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Crud\\DetailPage.php(191): MoonShine\\Laravel\\Resources\\CrudResource->getDetailFields()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Crud\\DetailPage.php(98): MoonShine\\Laravel\\Pages\\Crud\\DetailPage->getDetailComponents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(213): MoonShine\\Laravel\\Pages\\Crud\\DetailPage->mainLayer()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(202): MoonShine\\Core\\Pages\\Page->getLayerComponents()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Crud\\DetailPage.php(84): MoonShine\\Core\\Pages\\Page->getLayers()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(190): MoonShine\\Laravel\\Pages\\Crud\\DetailPage->components()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Layouts\\BaseLayout.php(233): MoonShine\\Core\\Pages\\Page->getComponents()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Layouts\\AppLayout.php(60): MoonShine\\Laravel\\Layouts\\BaseLayout->getContentComponents()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\app\\MoonShine\\Layouts\\MoonShineLayout.php(142): MoonShine\\Laravel\\Layouts\\AppLayout->build()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Pages\\Page.php(339): App\\MoonShine\\Layouts\\MoonShineLayout->build()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(188): MoonShine\\Core\\Pages\\Page->systemViewData()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(149): MoonShine\\Core\\Pages\\Page->toArray()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(142): MoonShine\\Core\\Pages\\Page->renderView()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(123): MoonShine\\Core\\Pages\\Page->resolveRender()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#72 {main}
"} 
[2025-07-08 09:28:50] local.ERROR: Method Illuminate\Validation\Validator::validateUsername does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Validation\\Validator::validateUsername does not exist. at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php:1680)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(685): Illuminate\\Validation\\Validator->__call()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(480): Illuminate\\Validation\\Validator->validateAttribute()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(557): Illuminate\\Validation\\Validator->fails()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(155): Illuminate\\Validation\\Validator->validate()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(126): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\app\\Http\\Controllers\\StudentController.php(34): Illuminate\\Http\\Request->__call()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\StudentController->login()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#55 {main}
"} 
[2025-07-08 09:30:11] local.ERROR: Method Illuminate\Validation\Validator::validateUsername does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Validation\\Validator::validateUsername does not exist. at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php:1680)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(685): Illuminate\\Validation\\Validator->__call()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(480): Illuminate\\Validation\\Validator->validateAttribute()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(557): Illuminate\\Validation\\Validator->fails()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(155): Illuminate\\Validation\\Validator->validate()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(126): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\app\\Http\\Controllers\\StudentController.php(34): Illuminate\\Http\\Request->__call()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\StudentController->login()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#55 {main}
"} 
[2025-07-08 09:45:53] local.ERROR: Method Illuminate\Validation\Validator::validateUsername does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Validation\\Validator::validateUsername does not exist. at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php:1680)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(685): Illuminate\\Validation\\Validator->__call()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(480): Illuminate\\Validation\\Validator->validateAttribute()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(557): Illuminate\\Validation\\Validator->fails()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(155): Illuminate\\Validation\\Validator->validate()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(126): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\app\\Http\\Controllers\\StudentController.php(34): Illuminate\\Http\\Request->__call()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\StudentController->login()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#55 {main}
"} 
[2025-07-09 11:45:56] local.ERROR: The given role or permission should use guard `` instead of `moonshine`. (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) (View: D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\moonshine\moonshine\src\UI\resources\views\components\components.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#63 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#73 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#82 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#83 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#84 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#85 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#86 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#87 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#88 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#89 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#90 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#91 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#92 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\2f84e384c238471d0dc9515a6fe93a37.php(46): Illuminate\\View\\Factory->renderComponent()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#83 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#84 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#85 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#86 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#87 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#88 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#89 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#90 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#91 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#92 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#93 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#94 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#95 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#96 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#97 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#98 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#99 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#100 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#101 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\2f84e384c238471d0dc9515a6fe93a37.php(46): Illuminate\\View\\Factory->renderComponent()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#83 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#84 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#85 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#86 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#87 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#88 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#89 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#90 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#91 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#92 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#93 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#94 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#95 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#96 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#97 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#98 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#99 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#100 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#101 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#102 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#103 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#104 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#105 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#106 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#107 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#108 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#109 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#110 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#111 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\cfb74a2c17605ed8b4fd2b265e6316db.php(49): Illuminate\\View\\Factory->renderComponent()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\2f84e384c238471d0dc9515a6fe93a37.php(46): Illuminate\\View\\Factory->renderComponent()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#83 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#84 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#85 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#86 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#87 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#88 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#89 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#90 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#91 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#92 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#93 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#94 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#95 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#96 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#97 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#98 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#99 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#100 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#101 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#102 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#103 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#104 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#105 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#106 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#107 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#108 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#109 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#110 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#111 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#112 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#113 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#114 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#115 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#116 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#117 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#118 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#119 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#120 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): The given role or permission should use guard `` instead of `moonshine`. (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) (View: D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\UI\\resources\\views\\components\\components.blade.php) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\GuardDoesNotMatch.php:12)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\cfb74a2c17605ed8b4fd2b265e6316db.php(49): Illuminate\\View\\Factory->renderComponent()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\2f84e384c238471d0dc9515a6fe93a37.php(46): Illuminate\\View\\Factory->renderComponent()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\d0956503ae696a640498502cdda02239.php(35): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\88d6fdc803413730a596afd35ab11c26.php(44): Illuminate\\View\\Factory->renderComponent()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(505): Illuminate\\View\\View->render()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(200): Illuminate\\View\\View->__toString()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\storage\\framework\\views\\aacb2f3c7dfebf6b6faf2d96628548bd.php(1): MoonShine\\UI\\Components\\MoonShineComponent->__toString()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#70 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#71 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#72 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(120): Illuminate\\View\\View->render()
#73 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Pages\\Page.php(62): Illuminate\\View\\View->fragmentIf()
#74 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Traits\\WithViewRenderer.php(125): MoonShine\\Laravel\\Pages\\Page->prepareRender()
#75 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): MoonShine\\Core\\Pages\\Page->render()
#76 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#77 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#78 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#79 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#80 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#81 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#83 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#84 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#85 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#86 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#87 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#88 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#89 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#90 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#91 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#92 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#93 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#94 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#95 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#96 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#97 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#98 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#99 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#100 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#101 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#102 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#103 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#104 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#105 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#106 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#107 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#108 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#109 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#110 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#111 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#112 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#113 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#114 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#115 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#116 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#117 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#118 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#119 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#120 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#121 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#122 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#123 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#124 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#125 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#126 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#127 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#128 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#129 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#130 {main}

[previous exception] Over 9 levels deep, aborting normalization"} 
[2025-07-09 13:05:59] local.ERROR: SQLSTATE[HY000]: General error: 1553 Cannot drop index 'school_classes_organization_id_grade_level_id_index': needed in a foreign key constraint (Connection: mysql, SQL: alter table `school_classes` drop index `school_classes_organization_id_grade_level_id_index`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1553 Cannot drop index 'school_classes_organization_id_grade_level_id_index': needed in a foreign key constraint (Connection: mysql, SQL: alter table `school_classes` drop index `school_classes_organization_id_grade_level_id_index`) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `sc...', Array, Object(Closure))
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `sc...', Array, Object(Closure))
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `sc...')
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('school_classes', Object(Closure))
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\database\\migrations\\2025_07_09_130300_remove_grade_level_id_from_school_classes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_09_1303...', Object(Closure))
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_09_1303...', Object(Closure))
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\ba\\\\calisma\\\\w...', 11, false)
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1553 Cannot drop index 'school_classes_organization_id_grade_level_id_index': needed in a foreign key constraint at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `sc...', Array)
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `sc...', Array, Object(Closure))
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `sc...', Array, Object(Closure))
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `sc...')
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('school_classes', Object(Closure))
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\database\\migrations\\2025_07_09_130300_remove_grade_level_id_from_school_classes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_09_1303...', Object(Closure))
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_09_1303...', Object(Closure))
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\ba\\\\calisma\\\\w...', 11, false)
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-09 13:06:24] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `school_classes_grade_level_id_foreign`; check that it exists (Connection: mysql, SQL: alter table `school_classes` drop foreign key `school_classes_grade_level_id_foreign`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `school_classes_grade_level_id_foreign`; check that it exists (Connection: mysql, SQL: alter table `school_classes` drop foreign key `school_classes_grade_level_id_foreign`) at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `sc...', Array, Object(Closure))
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `sc...', Array, Object(Closure))
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `sc...')
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('school_classes', Object(Closure))
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\database\\migrations\\2025_07_09_130300_remove_grade_level_id_from_school_classes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_09_1303...', Object(Closure))
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_09_1303...', Object(Closure))
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\ba\\\\calisma\\\\w...', 11, false)
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `school_classes_grade_level_id_foreign`; check that it exists at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `sc...', Array)
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `sc...', Array, Object(Closure))
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `sc...', Array, Object(Closure))
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `sc...')
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('school_classes', Object(Closure))
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\database\\migrations\\2025_07_09_130300_remove_grade_level_id_from_school_classes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_09_1303...', Object(Closure))
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_09_1303...', Object(Closure))
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\ba\\\\calisma\\\\w...', 11, false)
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-09 13:17:09] local.ERROR: App\MoonShine\Resources\GradeLevelResource {"userId":1,"exception":"[object] (Illuminate\\Container\\EntryNotFoundException(code: 0): App\\MoonShine\\Resources\\GradeLevelResource at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:841)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(168): Illuminate\\Container\\Container->get()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(208): MoonShine\\Core\\Core->resolveInstances()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): MoonShine\\Core\\Core->getResources()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Support\\src\\helpers.php(43): MoonShine\\Laravel\\MoonShineRequest->MoonShine\\Laravel\\Traits\\Request\\{closure}()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): memoize()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\PageController.php(17): MoonShine\\Laravel\\MoonShineRequest->getResource()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\PageController->__invoke()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#61 {main}

[previous exception] [object] (ErrorException(code: 0): include(D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer/../../app/MoonShine/Resources/GradeLevelResource.php): Failed to open stream: No such file or directory at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(576): include('...')
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Foundation\\Application->resolve()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(168): Illuminate\\Container\\Container->get()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(208): MoonShine\\Core\\Core->resolveInstances()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): MoonShine\\Core\\Core->getResources()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Support\\src\\helpers.php(43): MoonShine\\Laravel\\MoonShineRequest->MoonShine\\Laravel\\Traits\\Request\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): memoize()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\PageController.php(17): MoonShine\\Laravel\\MoonShineRequest->getResource()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\PageController->__invoke()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#70 {main}
"} 
[2025-07-09 13:24:37] local.ERROR: App\MoonShine\Resources\GradeLevelResource {"userId":1,"exception":"[object] (Illuminate\\Container\\EntryNotFoundException(code: 0): App\\MoonShine\\Resources\\GradeLevelResource at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:841)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(168): Illuminate\\Container\\Container->get()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(208): MoonShine\\Core\\Core->resolveInstances()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): MoonShine\\Core\\Core->getResources()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Support\\src\\helpers.php(43): MoonShine\\Laravel\\MoonShineRequest->MoonShine\\Laravel\\Traits\\Request\\{closure}()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): memoize()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\PageController.php(17): MoonShine\\Laravel\\MoonShineRequest->getResource()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\PageController->__invoke()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#61 {main}

[previous exception] [object] (ErrorException(code: 0): include(D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer/../../app/MoonShine/Resources/GradeLevelResource.php): Failed to open stream: No such file or directory at D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(576): include('...')
#3 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Foundation\\Application->resolve()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(168): Illuminate\\Container\\Container->get()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Core\\src\\Core.php(208): MoonShine\\Core\\Core->resolveInstances()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): MoonShine\\Core\\Core->getResources()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Support\\src\\helpers.php(43): MoonShine\\Laravel\\MoonShineRequest->MoonShine\\Laravel\\Traits\\Request\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Traits\\Request\\HasResourceRequest.php(23): memoize()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\PageController.php(17): MoonShine\\Laravel\\MoonShineRequest->getResource()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\PageController->__invoke()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#65 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 D:\\ba\\calisma\\web\\kitapokuma\\okuokuoku\\code\\moonaug\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#70 {main}
"} 
