@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source "../**/*.blade.php";
@source "../**/*.js";
@source "../**/*.vue";

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

@supports selector(:has(*)) {
  @media (min-width: 1024px) {
    .layout-wrapper:has(> .layout-menu) {
      padding-top: 0;
      padding-right: 0;
      padding-bottom: 0;
    }
  }
}

.layout-page {
  border-radius: 0; 
}