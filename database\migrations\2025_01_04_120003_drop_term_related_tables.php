<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop term_users table first (has foreign key to terms)
        Schema::dropIfExists('term_users');
        
        // Drop terms table
        Schema::dropIfExists('terms');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate terms table
        Schema::create('terms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('start_date');
            $table->date('end_date');
            $table->boolean('active')->default(true);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Add indexes
            $table->index('active');
            $table->index(['start_date', 'end_date']);
        });

        // Recreate term_users table
        Schema::create('term_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('role_id')->constrained('roles')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained('organizations')->onDelete('cascade');
            $table->foreignId('class_id')->nullable()->constrained('school_classes')->onDelete('cascade');
            $table->foreignId('term_id')->constrained('terms')->onDelete('cascade');
            $table->boolean('active')->default(true);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Add indexes and constraints
            $table->unique(['user_id', 'role_id', 'organization_id', 'class_id', 'term_id'], 'term_users_unique');
            $table->index(['term_id', 'active']);
            $table->index(['organization_id', 'role_id']);
            $table->index(['class_id', 'role_id']);
        });
    }
};
