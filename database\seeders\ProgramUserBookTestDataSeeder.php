<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\Program;
use App\Models\ProgramUserBook;
use App\Models\TermUser;
use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProgramUserBookTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user as creator
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Get the first program
        $program = Program::first();
        
        if (!$program) {
            $this->command->error('No programs found. Please create a program first.');
            return;
        }

        // Get student term users
        $students = TermUser::students()->take(3)->get();
        
        if ($students->count() < 1) {
            $this->command->error('No student term users found. Please create student term users first.');
            return;
        }

        // Get some books
        $books = Book::take(5)->get();
        
        if ($books->count() < 3) {
            $this->command->error('Need at least 3 books to create assignments.');
            return;
        }

        // Create various types of assignments
        $assignments = [];

        // Student 1: Completed assignments
        if ($students->count() >= 1) {
            $student1 = $students[0];
            
            // Completed assignment (finished 5 days ago)
            $assignments[] = [
                'program_id' => $program->id,
                'user_id' => $student1->id,
                'book_id' => $books[0]->id,
                'start_date' => now()->subDays(15)->toDateString(),
                'end_date' => now()->subDays(5)->toDateString(),
                'created_by' => $user->id,
            ];

            // Another completed assignment (finished yesterday)
            if ($books->count() >= 2) {
                $assignments[] = [
                    'program_id' => $program->id,
                    'user_id' => $student1->id,
                    'book_id' => $books[1]->id,
                    'start_date' => now()->subDays(10)->toDateString(),
                    'end_date' => now()->subDay()->toDateString(),
                    'created_by' => $user->id,
                ];
            }
        }

        // Student 2: Ongoing assignments
        if ($students->count() >= 2) {
            $student2 = $students[1];
            
            // Ongoing assignment (started 3 days ago)
            $assignments[] = [
                'program_id' => $program->id,
                'user_id' => $student2->id,
                'book_id' => $books[0]->id,
                'start_date' => now()->subDays(3)->toDateString(),
                'end_date' => null,
                'created_by' => $user->id,
            ];

            // Another ongoing assignment (started today)
            if ($books->count() >= 3) {
                $assignments[] = [
                    'program_id' => $program->id,
                    'user_id' => $student2->id,
                    'book_id' => $books[2]->id,
                    'start_date' => now()->toDateString(),
                    'end_date' => null,
                    'created_by' => $user->id,
                ];
            }
        }

        // Student 3: Overdue assignment
        if ($students->count() >= 3) {
            $student3 = $students[2];
            
            // Overdue assignment (started 35 days ago, not completed)
            $assignments[] = [
                'program_id' => $program->id,
                'user_id' => $student3->id,
                'book_id' => $books[0]->id,
                'start_date' => now()->subDays(35)->toDateString(),
                'end_date' => null,
                'created_by' => $user->id,
            ];
        }

        // Create the assignments
        foreach ($assignments as $assignmentData) {
            // Check if this assignment already exists
            $exists = ProgramUserBook::where('program_id', $assignmentData['program_id'])
                                    ->where('user_id', $assignmentData['user_id'])
                                    ->where('book_id', $assignmentData['book_id'])
                                    ->exists();
            
            if (!$exists) {
                $assignment = ProgramUserBook::create($assignmentData);
                
                $student = TermUser::find($assignmentData['user_id']);
                $book = Book::find($assignmentData['book_id']);
                $status = $assignmentData['end_date'] ? 'completed' : 'ongoing';
                
                $this->command->info("Created {$status} assignment: '{$book->name}' for student '{$student->user->name}'");
            } else {
                $student = TermUser::find($assignmentData['user_id']);
                $book = Book::find($assignmentData['book_id']);
                $this->command->info("Assignment already exists: '{$book->name}' for student '{$student->user->name}'");
            }
        }

        $this->command->info('Program user book test data seeded successfully!');
        
        // Display statistics
        $stats = ProgramUserBook::getProgramStats($program->id);
        $this->command->info("Program Statistics:");
        $this->command->info("- Total assignments: {$stats['total']}");
        $this->command->info("- Completed: {$stats['completed']}");
        $this->command->info("- Ongoing: {$stats['ongoing']}");
        $this->command->info("- Overdue: {$stats['overdue']}");
        $this->command->info("- Completion rate: {$stats['completion_rate']}%");
    }
}
