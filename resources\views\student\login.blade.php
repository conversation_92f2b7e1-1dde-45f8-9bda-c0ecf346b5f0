@extends('layouts.student')

@section('title', __('student.login_title'))

@section('content')
<div class="game-container flex items-center justify-center min-h-screen">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="achievement-badge bg-game-blue mx-auto mb-4" style="width: 80px; height: 80px; font-size: 32px;">
                🎮
            </div>
            <h1 class="text-3xl font-black text-white mb-2">
                {{ __('student.welcome_back') }}
            </h1>
            <p class="text-white text-lg font-semibold opacity-90">
                {{ __('student.login_subtitle') }}
            </p>
        </div>

        <!-- Login Form -->
        <div class="game-card p-8">
            <form method="POST" action="{{ route('student.login') }}" id="login-form">
                @csrf
                
                <!-- Email Field -->
                <div class="mb-6">
                    <label for="email" class="block text-sm font-bold text-gray-700 mb-2">
                        {{ __('email') }}
                    </label>
                    <div class="relative">
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            value="{{ old('email') }}"
                            class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-game-blue focus:outline-none transition-colors text-lg font-semibold @error('email') border-red-500 @enderror"
                            placeholder="{{ __('student.enter_email') }}"
                            required
                            autocomplete="email"
                        >
                        <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                            <div class="achievement-badge bg-game-blue w-6 h-6 text-xs">
                                ✉️
                            </div>
                        </div>
                    </div>
                    @error('email')
                        <p class="text-red-500 text-sm font-semibold mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password Field -->
                <div class="mb-6">
                    <label for="password" class="block text-sm font-bold text-gray-700 mb-2">
                        {{ __('password') }}
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                            class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-game-blue focus:outline-none transition-colors text-lg font-semibold @error('password') border-red-500 @enderror"
                            placeholder="{{ __('student.enter_password') }}"
                            required
                            autocomplete="current-password"
                        >
                        <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                            <div class="achievement-badge bg-game-green w-6 h-6 text-xs">
                                🔒
                            </div>
                        </div>
                    </div>
                    @error('password')
                        <p class="text-red-500 text-sm font-semibold mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Remember Me -->
                <div class="mb-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            name="remember" 
                            class="w-5 h-5 text-game-blue border-2 border-gray-300 rounded focus:ring-game-blue focus:ring-2"
                            {{ old('remember') ? 'checked' : '' }}
                        >
                        <span class="ml-3 text-sm font-semibold text-gray-700">
                            {{ __('student.remember_me') }}
                        </span>
                    </label>
                </div>

                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="mb-6 p-4 bg-red-50 border-2 border-red-200 rounded-2xl">
                        <div class="flex items-center">
                            <div class="achievement-badge bg-game-red w-8 h-8 text-sm mr-3">
                                ⚠️
                            </div>
                            <div>
                                <h3 class="text-red-800 font-bold text-sm">{{ __('student.login_error') }}</h3>
                                <p class="text-red-600 text-sm">{{ __('student.check_credentials') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Login Button -->
                <button type="submit" class="game-btn w-full mb-4" id="login-btn">
                    <span id="login-text">{{ __('student.start_adventure') }}</span>
                    <div id="login-loading" class="loading-dots hidden ml-2">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                </button>

                <!-- Help Text -->
                <div class="text-center">
                    <p class="text-gray-600 text-sm font-semibold">
                        {{ __('student.need_help') }}
                        <a href="#" class="text-game-blue hover:text-game-purple transition-colors">
                            {{ __('student.contact_teacher') }}
                        </a>
                    </p>
                </div>
            </form>
        </div>

        <!-- Fun Motivational Message -->
        <div class="game-card p-6 mt-6 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200">
            <div class="flex items-center">
                <div class="achievement-badge bg-game-orange mr-4" style="width: 50px; height: 50px; font-size: 20px;">
                    🌟
                </div>
                <div>
                    <h3 class="text-game-blue font-black text-lg">{{ __('student.daily_motivation') }}</h3>
                    <p class="text-gray-700 font-semibold text-sm" id="motivation-text">
                        {{ __('student.motivation_1') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Motivational messages
    const motivations = [
        "{{ __('student.motivation_1') }}",
        "{{ __('student.motivation_2') }}",
        "{{ __('student.motivation_3') }}",
        "{{ __('student.motivation_4') }}",
        "{{ __('student.motivation_5') }}"
    ];

    // Rotate motivational messages
    function rotateMotivation() {
        const motivationElement = document.getElementById('motivation-text');
        const randomIndex = Math.floor(Math.random() * motivations.length);
        motivationElement.style.opacity = '0';
        setTimeout(() => {
            motivationElement.textContent = motivations[randomIndex];
            motivationElement.style.opacity = '1';
        }, 300);
    }

    // Handle form submission
    function handleFormSubmission() {
        const form = document.getElementById('login-form');
        const loginBtn = document.getElementById('login-btn');
        const loginText = document.getElementById('login-text');
        const loginLoading = document.getElementById('login-loading');

        form.addEventListener('submit', function(e) {
            // Show loading state
            loginText.textContent = '{{ __("student.logging_in") }}';
            loginLoading.classList.remove('hidden');
            loginBtn.disabled = true;
            
            hapticFeedback('medium');
            showLoading();
        });
    }

    // Add input animations
    function addInputAnimations() {
        const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                hapticFeedback('light');
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    }

    // Initialize login page
    document.addEventListener('DOMContentLoaded', function() {
        handleFormSubmission();
        addInputAnimations();
        
        // Rotate motivation every 5 seconds
        setInterval(rotateMotivation, 5000);
        
        // Focus on email field
        document.getElementById('email').focus();
    });
</script>
@endpush
@endsection
