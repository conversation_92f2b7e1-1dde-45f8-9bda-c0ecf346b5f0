<?php

return [
	// System
	'system' => 'System',
	'users' => 'Users',
	'my_students' => 'My Students',
	'roles' => 'Roles',
	'admins_title' => 'Administrators',

	// Academic
	'academic' => 'Academic',
	'organizations' => 'Organizations',

	'school_classes' => 'Classes',

	// Books
	'books' => 'Books',
	'authors' => 'Authors',
	'publishers' => 'Publishers',
	'book_authors' => 'Book Authors',

	// Gamification
	'gamification' => 'Gamification',
	'stories' => 'Stories',
	'story_rules' => 'Story Rules',
	'story_rule_details' => 'Story Rule Details',
	'story_chapters' => 'Story Chapters',
	'story_characters' => 'Story Characters',
	'story_character_stages' => 'Character Stages',
	'story_achievements' => 'Story Achievements',
	'story_books' => 'Story Books',

	// Reading Programs
	'reading_programs' => 'Reading Programs',
	'programs' => 'Programs',
	'program_schools' => 'Program Schools',
	'program_classes' => 'Program Classes',
	'program_books' => 'Program Books',
	'program_teams' => 'Program Teams',
	'program_team_members' => 'Team Members',
	'program_user_levels' => 'User Progress',
	'program_user_achievements' => 'User Achievements',
	'program_user_characters' => 'User Characters',
	'program_user_maps' => 'User Maps',
	'program_user_points' => 'User Points',
	'program_user_books' => 'User Book Assignments',

	// Program Tasks
	'program_tasks' => 'Program Tasks',
	'program_task_instances' => 'Task Assignments',
	'program_task_actions' => 'Task Actions',

	// Assessment System
	'assessment_system' => 'Assessment System',
	'book_questions' => 'Book Questions',
	'book_words' => 'Book Vocabulary',
	'book_activity_types' => 'Activity Types',
	'program_book_quizzes' => 'Book Quizzes',
	'program_book_activities' => 'Book Activities',

	// Reading Log System
	'reading_log_system' => 'Reading Log System',
	'program_reading_logs' => 'Reading Logs',

	// Program Fields
	'program_name' => 'Program Name',
	'program_description' => 'Description',
	'story' => 'Story',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'is_active' => 'Active',
	'status' => 'Status',
	'duration' => 'Duration',
	'participating_schools' => 'Participating Schools',
	'participating_classes' => 'Participating Classes',
	'associated_books' => 'Associated Books',
	'team_name' => 'Team Name',
	'current_chapter' => 'Current Chapter',
	'current_stage' => 'Current Stage',
	'earned_at' => 'Earned At',
	'points' => 'Points',
	'point_source' => 'Point Source',
	'item_type' => 'Item Type',
	'coordinates' => 'Coordinates',
	'x_coordinate' => 'X Coordinate',
	'y_coordinate' => 'Y Coordinate',

	// Task Fields
	'task_name' => 'Task Name',
	'task_description' => 'Task Description',
	'task_type' => 'Task Type',
	'is_recurring' => 'Recurring',
	'recurrence_pattern' => 'Recurrence Pattern',
	'page_start' => 'Start Page',
	'page_end' => 'End Page',
	'page_range' => 'Page Range',
	'assigned_via' => 'Assigned Via',
	'task_instance' => 'Task Instance',
	'action_type' => 'Action Type',
	'action_date' => 'Action Date',
	'points_awarded' => 'Points Awarded',
	'completed_by' => 'Completed By',
	'action_summary' => 'Action Summary',
	'days_remaining' => 'Days Remaining',
	'scheduling' => 'Scheduling',
	'book_settings' => 'Book Settings',

	// Assessment Fields
	'question_text' => 'Question Text',
	'correct_answer' => 'Correct Answer',
	'incorrect_answer' => 'Incorrect Answer',
	'question_image_url' => 'Question Image URL',
	'question_audio_url' => 'Question Audio URL',
	'question_video_url' => 'Question Video URL',
	'page_reference' => 'Page Reference',
	'word' => 'Word',
	'definition' => 'Definition',
	'synonym' => 'Synonym',
	'antonym' => 'Antonym',
	'word_display' => 'Word Display',
	'category' => 'Category',
	'min_word_count' => 'Minimum Word Count',
	'max_word_count' => 'Maximum Word Count',
	'word_count_range' => 'Word Count Range',
	'points_base' => 'Base Points',
	'activity_type' => 'Activity Type',
	'activity_content' => 'Activity Content',
	'word_count' => 'Word Count',
	'quiz_type' => 'Quiz Type',
	'total_questions' => 'Total Questions',
	'correct_answers' => 'Correct Answers',
	'score_percentage' => 'Score Percentage',
	'passing_score' => 'Passing Score',
	'is_passed' => 'Passed',
	'attempt_number' => 'Attempt Number',
	'time_limit_minutes' => 'Time Limit (Minutes)',
	'duration_minutes' => 'Duration (Minutes)',
	'reviewed_by' => 'Reviewed By',
	'reviewed_at' => 'Reviewed At',
	'feedback' => 'Feedback',
	'is_reviewed' => 'Reviewed',
	'media_type' => 'Media Type',
	'answer_options' => 'Answer Options',
	'media_content' => 'Media Content',
	'instructions' => 'Instructions',

	// Reading Log Fields
	'reading_date' => 'Reading Date',
	'start_page' => 'Start Page',
	'end_page' => 'End Page',
	'page_range' => 'Page Range',
	'pages_read' => 'Pages Read',
	'reading_duration_minutes' => 'Reading Duration (Minutes)',
	'reading_speed' => 'Reading Speed (Pages/Min)',
	'reading_notes' => 'Reading Notes',
	'is_verified' => 'Verified',
	'verified_by' => 'Verified By',
	'verified_at' => 'Verified At',
	'points_awarded' => 'Points Awarded',
	'reading_details' => 'Reading Details',
	'verification' => 'Verification',
	'reading_streak' => 'Reading Streak',
	'total_sessions' => 'Total Sessions',
	'average_pages_per_session' => 'Average Pages per Session',
	'completion_percentage' => 'Completion Percentage',

	// Program Status
	'active' => 'Active',
	'inactive' => 'Inactive',
	'upcoming' => 'Upcoming',
	'completed' => 'Completed',

	// Point Sources
	'achievement' => 'Achievement',
	'task' => 'Task',
	'quest' => 'Quest',
	'reading' => 'Reading',
	'bonus' => 'Bonus',

	// Task Types
	'reading_log' => 'Reading Log',
	'activity' => 'Activity',
	'question' => 'Question',
	'physical' => 'Physical',

	// Task Status
	'pending' => 'Pending',
	'missed' => 'Missed',
	'excused' => 'Excused',
	'reassigned' => 'Reassigned',
	'upcoming' => 'Upcoming',
	'expired' => 'Expired',

	// Recurrence Patterns
	'daily' => 'Daily',
	'weekly' => 'Weekly',
	'one_time' => 'One-time',

	// Assignment Types
	'individual' => 'Individual',
	'team' => 'Team',

	// Activity Categories
	'vocabulary' => 'Vocabulary',
	'spelling' => 'Spelling',
	'writing' => 'Writing',
	'comprehension' => 'Comprehension',
	'creative' => 'Creative',

	// Quiz Types
	'completion' => 'Book Completion Quiz',
	'daily_reading' => 'Daily Reading Quiz',
	'practice' => 'Practice Quiz',

	// Difficulty Levels
	'easy' => 'Easy',
	'medium' => 'Medium',
	'hard' => 'Hard',

	// Item Types
	'item' => 'Item',
	'badge' => 'Badge',
	'reward' => 'Reward',
	'trophy' => 'Trophy',
	'collectible' => 'Collectible',

	// Common Actions
	'select_program' => 'Select Program',
	'select_story' => 'Select Story',
	'select_student' => 'Select Student',
	'select_school' => 'Select School',
	'select_chapter' => 'Select Chapter',
	'select_achievement' => 'Select Achievement',
	'enter_name' => 'Enter Name',
	'enter_name_surname' => 'Enter Name and Surname',
	'enter_description' => 'Enter Description',
	'enter_team_name' => 'Enter Team Name',
	'member_count' => 'Member Count',
	'achievement_type' => 'Achievement Type',
	'select_class' => 'Select Class',
	'select_book' => 'Select Book',
	'select_team' => 'Select Team',
	'select_character' => 'Select Character',
	'item_id' => 'Item ID',
	'isbn' => 'ISBN',

	// Additional common fields
	'title' => 'Title',
	'enter_title' => 'Enter Title',
	'enter_user_title' => 'Enter Title',
	'enter_email' => 'Enter Email',
	'select_role' => 'Select Role',
	'select_user' => 'Select User',
	'main_information' => 'Main Information',
	'audit_info' => 'Audit Information',
	'user' => 'User',
	'role' => 'Role',
	'email' => 'Email',
	'username' => 'Username',
	'enter_username' => 'Enter Username',

	// Tags
	'tags' => 'Tags',
	'tag_values' => 'Tag Values',
	
	// Common Fields
	'id' => 'ID',
	'name' => 'Name',
	'name_surname' => 'Name & Surname',
	'user_title' => 'Title',
	'email' => 'Email',
	'password' => 'Password',
	'password_repeat' => 'Repeat Password',
	'description' => 'Description',
	'active' => 'Active',
	'level' => 'Level',
	'type' => 'Type',
	'parent' => 'Parent',
	'children' => 'Children',
	'role' => 'Role',
	'user' => 'User',
	'organization' => 'Organization',
	'class' => 'Class',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'status' => 'Status',
	'created_at' => 'Created At',
	'updated_at' => 'Updated At',
	'created_by' => 'Created By',
	'updated_by' => 'Updated By',
	
	// Book Fields
	'isbn' => 'ISBN',
	'publisher' => 'Publisher',
	'page_count' => 'Page Count',
	'year_of_publish' => 'Publication Year',
	'author' => 'Author',
	'book' => 'Book',

	// Gamification Fields
	'story' => 'Story',
	'title' => 'Title',
	'cover_image' => 'Cover Image',
	'map_grid_rows' => 'Map Grid Rows',
	'map_grid_columns' => 'Map Grid Columns',
	'map_background_image' => 'Map Background Image',
	'rule_type' => 'Rule Type',
	'quantity' => 'Quantity',
	'required_type' => 'Required Type',
	'required_id' => 'Required ID',
	'sequence' => 'Sequence',
	'sequence_hint' => 'Order of books in the story (0 = auto-assign next available)',
	'book_already_in_story' => 'This book is already associated with this story',
	'sequence_already_exists' => 'This sequence number already exists for this story',
	'is_first' => 'Is First',
	'is_last' => 'Is Last',
	'yes' => 'Yes',
	'no' => 'No',
	'assignment_info' => 'Assignment Information',
	'assignment_date_hint' => 'Date when the book was assigned to the student',
	'completion_date_hint' => 'Date when the student completed reading the book (leave empty if ongoing)',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'status' => 'Status',
	'completed' => 'Completed',
	'overdue' => 'Overdue',
	'ongoing' => 'Ongoing',
	'unknown' => 'Unknown',
	'duration_days' => 'Duration',
	'days' => 'days',
	'days_ongoing' => 'days (ongoing)',
	'is_completed' => 'Is Completed',
	'is_overdue' => 'Is Overdue',
	'must_be_student' => 'Selected user must be a student',
	'book_already_assigned' => 'This book is already assigned to this student in this program',
	'student_email' => 'Student Email',
	'student_class' => 'Student Class',
	'student_organization' => 'Student Organization',
	'dates' => 'Dates',
	'unlock_rule' => 'Unlock Rule',
	'map_start_x' => 'Map Start X',
	'map_start_y' => 'Map Start Y',
	'map_end_x' => 'Map End X',
	'map_end_y' => 'Map End Y',
	'base_image' => 'Base Image',
	'character' => 'Character',
	'stage_number' => 'Stage Number',
	'image' => 'Image',
	'abilities' => 'Abilities',
	'achievement' => 'Achievement',
	'is_dynamic_position' => 'Dynamic Position',
	'chapter' => 'Chapter',
	'stage' => 'Stage',
	'rule' => 'Rule',
	'map_coordinates' => 'Map Coordinates',
	'map_dimensions' => 'Map Dimensions',
	'chapter_count' => 'Chapter Count',
	'character_count' => 'Character Count',
	'achievement_count' => 'Achievement Count',
	'stage_count' => 'Stage Count',
	'progression_summary' => 'Progression Summary',
	'full_title' => 'Full Title',
	'full_name' => 'Full Name',
	'abilities_text' => 'Abilities Text',
	'type_display_name' => 'Type Display Name',
	'info' => 'Info',
	'required_item_name' => 'Required Item Name',
	
	// Counts
	'users_count' => 'Users Count',
	'books_count' => 'Books Count',
	'student_count' => 'Student Count',
	'duration_days' => 'Duration (Days)',
	
	// Relationships
	'relationships' => 'Relationships',
	'organization_assignments' => 'Organization/Role',
	'class_assignments' => 'Classes',
	'assignment_info' => 'Assignment Information',
	'summary' => 'Summary',
	
	// Info Sections
	'main_information' => 'Main Information',
	'audit_info' => 'Audit Information',
	'user_info' => 'User Information',
	'role_info' => 'Role Information',
	'organization_info' => 'Organization Information',
	'book_info' => 'Book Information',
	'author_info' => 'Author Information',
	'publisher_info' => 'Publisher Information',
	'class_info' => 'Class Information',
	'grade_level_info' => 'Grade Level Information',
	'assignments' => 'Assignments',
	
	// Placeholders
	'enter_name' => 'Enter name',
	'enter_title' => 'Enter title',
	'enter_email' => 'Enter email',
	'enter_description' => 'Enter description',
	'select_role' => 'Select role',
	'select_level' => 'Select level',
	'select_type' => 'Select type',
	'select_parent' => 'Select parent',
	'select_user' => 'Select user',
	'select_organization' => 'Select organization',
	'select_class' => 'Select class',
	'select_publisher' => 'Select publisher',
	'select_author' => 'Select author',
	'select_book' => 'Select book',
	'select_grade_level' => 'Select grade level',
	'enter_isbn' => 'Enter ISBN',
	'enter_page_count' => 'Enter page count',
	'enter_year' => 'Enter year',
	'enter_title' => 'Enter title',
	'select_story' => 'Select story',
	'select_rule_type' => 'Select rule type',
	'select_required_type' => 'Select required type',
	'enter_quantity' => 'Enter quantity',
	'enter_sequence' => 'Enter sequence',
	'select_unlock_rule' => 'Select unlock rule',
	'select_character' => 'Select character',
	'enter_stage_number' => 'Enter stage number',
	'select_achievement_type' => 'Select achievement type',

	// Task Placeholders
	'enter_task_name' => 'Enter task name',
	'enter_task_description' => 'Enter task description',
	'select_task_type' => 'Select task type',
	'select_recurrence_pattern' => 'Select recurrence pattern',
	'enter_page_start' => 'Enter start page',
	'enter_page_end' => 'Enter end page',
	'enter_points_awarded' => 'Enter points awarded',
	'select_task' => 'Select task',
	'select_student' => 'Select student',
	'select_task_instance' => 'Select task instance',
	'select_action_type' => 'Select action type',
	'select_completed_by' => 'Select completed by',
	'enter_notes' => 'Enter notes',

	// Assessment Placeholders
	'enter_question_text' => 'Enter question text',
	'enter_correct_answer' => 'Enter correct answer',
	'enter_incorrect_answer' => 'Enter incorrect answer',
	'enter_image_url' => 'Enter image URL',
	'enter_audio_url' => 'Enter audio URL',
	'enter_video_url' => 'Enter video URL',
	'enter_word' => 'Enter word',
	'enter_definition' => 'Enter definition',
	'enter_synonym' => 'Enter synonym',
	'enter_antonym' => 'Enter antonym',
	'enter_page_reference' => 'Enter page reference',
	'select_category' => 'Select category',
	'enter_activity_name' => 'Enter activity name',
	'enter_activity_description' => 'Enter activity description',
	'enter_min_word_count' => 'Enter minimum word count',
	'enter_max_word_count' => 'Enter maximum word count',
	'enter_base_points' => 'Enter base points',
	'select_quiz_type' => 'Select quiz type',
	'enter_total_questions' => 'Enter total questions',
	'enter_passing_score' => 'Enter passing score',
	'enter_time_limit' => 'Enter time limit',
	'select_activity_type' => 'Select activity type',
	'enter_activity_content' => 'Enter activity content',
	'enter_word_count' => 'Enter word count',
	'enter_points_earned' => 'Enter points earned',
	'select_reviewer' => 'Select reviewer',
	'enter_feedback' => 'Enter feedback',
	'select_task_instance' => 'Select task instance',

	// Reading Log Placeholders
	'select_reading_date' => 'Select reading date',
	'enter_start_page' => 'Enter start page',
	'enter_end_page' => 'Enter end page',
	'enter_reading_duration' => 'Enter reading duration in minutes',
	'enter_reading_notes' => 'Enter reading notes or thoughts',
	'select_verifier' => 'Select verifier',
	'enter_points_awarded' => 'Enter points awarded',

	// Reading Log Messages
	'reading_log_already_exists_for_date' => 'Reading log already exists for this date',
	'reading_log_created_successfully' => 'Reading log created successfully',
	'reading_log_verified_successfully' => 'Reading log verified successfully',
	'bulk_verification_completed' => 'Bulk verification completed',

	// Attributes
	'hierarchy_path' => 'Hierarchy Path',
	'role_level' => 'Role Level',
	'full_name' => 'Full Name',
	'display_name' => 'Display Name',
	'publication_info' => 'Publication Info',
	'author_names' => 'Author Names',
	'difficulty' => 'Difficulty',
	'estimated_reading_time' => 'Estimated Reading Time',
	'age' => 'Age',
	'usage_count' => 'Usage Count',
	'tag_type' => 'Tag Type',
	'taggable_type' => 'Taggable Type',
	'formatted_name' => 'Formatted Name',
	'total_pages' => 'Total Pages',
	'average_pages' => 'Average Pages',
	'books_list' => 'Books List',
	
	// Privacy Agreement
	'privacy_agreement_title' => 'Privacy Policy Agreement',
	'privacy_policy_title' => 'Privacy Policy',
	'privacy_agreement_consent' => 'I accept the privacy policy and terms of use',
	'accept_and_continue' => 'Accept and Continue',
	'privacy_consent_required' => 'You must accept the privacy policy to continue.',
	'privacy_agreement_accepted' => 'Privacy policy accepted successfully.',
	'privacy_agreement_error' => 'An error occurred while processing your consent. Please try again.',
	'authentication_required' => 'Authentication required.',
	'last_updated' => 'Last Updated',
	'version' => 'Version',

	// Privacy Policy Content
	'privacy_policy_intro' => 'This privacy policy explains how we collect, use, and protect your personal information when you use our reading tracker application.',
	'data_collection_title' => '1. Data Collection',
	'data_collection_content' => 'We collect information you provide directly to us, such as your name, email address, reading progress, and usage data to provide and improve our services.',
	'data_usage_title' => '2. Data Usage',
	'data_usage_content' => 'We use your information to provide educational services, track reading progress, generate reports, and improve our application functionality.',
	'data_sharing_title' => '3. Data Sharing',
	'data_sharing_content' => 'We do not sell or share your personal information with third parties except as necessary to provide our services or as required by law.',
	'data_security_title' => '4. Data Security',
	'data_security_content' => 'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
	'user_rights_title' => '5. Your Rights',
	'user_rights_content' => 'You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.',
	'contact_info_title' => '6. Contact Information',
	'contact_info_content' => 'If you have questions about this privacy policy, please contact your system administrator or school administration.',

	// User Agreements
	'user_agreements' => 'User Agreements',
	'agreement_type' => 'Agreement Type',
	'accepted_at' => 'Accepted At',
	'ip_address' => 'IP Address',
	'privacy_policy' => 'Privacy Policy',
	'terms_of_service' => 'Terms of Service',
	'cookie_policy' => 'Cookie Policy',
	'enter_version' => 'Enter version',
	'enter_ip_address' => 'Enter IP address',

	// Actions
	'create' => 'Create',
	'edit' => 'Edit',
	'cancel' => 'Cancel',
	'delete' => 'Delete',
	'view' => 'View',
	'save' => 'Save',
	'cancel' => 'Cancel',
	'activate' => 'Activate',
	'deactivate' => 'Deactivate',
	
	// Messages
	'no_records' => 'No records found',
	'loading' => 'Loading...',
	'saved_successfully' => 'Saved successfully',
	'deleted_successfully' => 'Deleted successfully',
	'error_occurred' => 'An error occurred',
	
	// Validation
	'required' => 'This field is required',
	'email_invalid' => 'Please enter a valid email address',
	'password_min' => 'Password must be at least 8 characters',
	'password_confirmed' => 'Passwords do not match',
	'unique' => 'This value is already taken',
	'exists' => 'The selected value is invalid',
	
	// Role Levels
	'system_admin' => 'System Administrator',
	'school_admin' => 'Organization Administrator',
	'teacher' => 'Teacher',
	'student' => 'Student',
	
	// Organization Types
	'school_group' => 'School Group',
	'school' => 'School',
	
	// Tag Types
	'organization_tags' => 'Organization Tags',
	'book_tags' => 'Book Tags',
	'user_tags' => 'User Tags',
	
	// Status Values
	'current' => 'Current',
	'future' => 'Future',
	'past' => 'Past',
	'easy' => 'Easy',
	'medium' => 'Medium',
	'hard' => 'Hard',
	'very_hard' => 'Very Hard',

	// Rule Types
	'points' => 'Points',
	'achievement_count' => 'Achievement Count',
	'book_count' => 'Book Count',
	'specific_achievements' => 'Specific Achievements',
	'specific_books' => 'Specific Books',
	'chapter_completion' => 'Chapter Completion',
	'time_based' => 'Time Based',

	// Required Types
	'required_achievement' => 'Required Achievement',
	'required_book' => 'Required Book',
	'required_chapter' => 'Required Chapter',
	'required_character_stage' => 'Required Character Stage',

	// Achievement Types
	'item' => 'Item',
	'badge' => 'Badge',
	'reward' => 'Reward',
	'trophy' => 'Trophy',
	'collectible' => 'Collectible',
	
	// Time
	'hours' => 'hours',
	'minutes' => 'minutes',
	'days' => 'days',
	'years' => 'years',
];
