<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrganizationUser extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'organization_id',
        'role_id',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the role.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to filter by role.
     */
    public function scopeByRole($query, $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    /**
     * Get the display name for this assignment.
     */
    public function getDisplayNameAttribute(): string
    {
        $parts = [
            $this->user->name,
            $this->role->name,
            $this->organization->name,
        ];

        return implode(' - ', array_filter($parts));
    }

    /**
     * Get assignment summary.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->user->name . ' as ' . $this->role->name;
        
        if ($this->organization) {
            $summary .= ' at ' . $this->organization->name;
        }
        
        return $summary;
    }

    /**
     * Activate this assignment.
     */
    public function activate(): bool
    {
        return $this->update(['active' => true]);
    }

    /**
     * Deactivate this assignment.
     */
    public function deactivate(): bool
    {
        return $this->update(['active' => false]);
    }

    /**
     * Check if this assignment is active.
     */
    public function isActive(): bool
    {
        return $this->active;
    }
}
