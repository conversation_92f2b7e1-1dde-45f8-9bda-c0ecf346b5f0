<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

use Illuminate\Support\Facades\Auth;

class TestMoonShineAccess extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:moonshine-access';

    /**
     * The console command description.
     */
    protected $description = 'Test MoonShine access for different roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== MoonShine Access Test ===');
        $this->newLine();

        // Test Teacher Access
        $teacher = User::whereHas('roles', function($q) {
            $q->where('name', 'Teacher');
        })->first();

        if ($teacher) {
            $this->info("Testing Teacher Access ({$teacher->name}):");
            
            // Login as teacher
            Auth::guard('moonshine')->login($teacher);
            
            // Check if teacher can access user management
            $this->line("  - Logged in as: " . auth('moonshine')->user()->name);
            $this->line("  - Role: " . auth('moonshine')->user()->role->name);
            $this->line("  - Is Teacher: " . (auth('moonshine')->user()->isTeacher() ? 'YES' : 'NO'));
            
            // Test basic User query
            $userCount = User::count();
            $this->line("  - Total users in database: {$userCount}");
            
            // Test role-based filtering manually
            $studentsInTeacherClasses = $this->getStudentsForTeacher($teacher);
            $this->line("  - Students teacher should see: {$studentsInTeacherClasses}");
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();

        // Test Student Access
        $student = User::whereHas('roles', function($q) {
            $q->where('name', 'Student');
        })->first();

        if ($student) {
            $this->info("Testing Student Access ({$student->name}):");
            
            // Login as student
            Auth::guard('moonshine')->login($student);
            
            $this->line("  - Logged in as: " . auth('moonshine')->user()->name);
            $this->line("  - Role: " . auth('moonshine')->user()->role->name);
            $this->line("  - Is Student: " . (auth('moonshine')->user()->isStudent() ? 'YES' : 'NO'));
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        return 0;
    }

    private function getStudentsForTeacher(User $teacher): int
    {
        // Get teacher's assigned classes through user_classes table
        $teacherClassIds = \App\Models\UserClass::where('user_id', $teacher->id)
            ->where('active', true)
            ->pluck('class_id')
            ->filter()
            ->toArray();

        if (empty($teacherClassIds)) {
            return 0;
        }

        // Count students in those classes
        return \App\Models\UserClass::whereIn('class_id', $teacherClassIds)
            ->where('active', true)
            ->whereHas('user.roles', function ($q) {
                $q->where('name', 'Student');
            })
            ->count();
    }
}
