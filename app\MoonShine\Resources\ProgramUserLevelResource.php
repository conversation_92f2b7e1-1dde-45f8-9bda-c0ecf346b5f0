<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserLevel;
use App\Models\StoryChapter;
use App\Models\User;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\StoryChapterResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramUserLevel>
 */
class ProgramUserLevelResource extends BaseResource
{
    protected string $model = ProgramUserLevel::class;

    protected array $with = ['program',  'storyChapter', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_levels');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name)
                ->sortable(),
            BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                formatted: fn(StoryChapter $chapter) => $chapter->title)
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.users'), 'user',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                    formatted: fn(StoryChapter $chapter) => $chapter->title,
                    resource: StoryChapterResource::class)
                    ->required()
                    ->placeholder(__('admin.select_chapter')),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name),
            BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                formatted: fn(StoryChapter $chapter) => $chapter->title),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'user_id' => ['required', 'exists:users,id'],
            'story_chapter_id' => ['required', 'exists:story_chapters,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
