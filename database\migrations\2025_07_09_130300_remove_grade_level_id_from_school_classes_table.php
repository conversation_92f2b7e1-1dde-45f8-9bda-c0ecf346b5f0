<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('school_classes', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['grade_level_id']);

            // Drop the index
            $table->dropIndex(['organization_id', 'grade_level_id']);

            // Drop the unique constraint that includes grade_level_id
            $table->dropUnique(['name', 'organization_id', 'grade_level_id']);

            // Drop the grade_level_id column
            $table->dropColumn('grade_level_id');

            // Add new unique constraint without grade_level_id
            $table->unique(['name', 'organization_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('school_classes', function (Blueprint $table) {
            // Add back the grade_level_id column
            $table->foreignId('grade_level_id')->constrained('grade_levels')->onDelete('cascade');

            // Drop the unique constraint without grade_level_id
            $table->dropUnique(['name', 'organization_id']);

            // Add back the original constraints
            $table->index(['organization_id', 'grade_level_id']);
            $table->unique(['name', 'organization_id', 'grade_level_id']);
        });
    }
};
