<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Organization;
use App\Models\User;
use App\Models\OrganizationUser;
use App\Models\Role;
use App\Models\SchoolClass;
use App\Models\UserClass;


use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Email;
use MoonShine\UI\Fields\Password;
use MoonShine\UI\Fields\PasswordRepeat;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRoleFormComponent;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Fields\Relationships\RelationRepeater;
use MoonShine\UI\Fields\ID;

#[Icon('users')]
class UserResource extends BaseResource
{
    use WithRoleFormComponent;

    //WARN: We modify getRoles method in vendor\sweet1s\moonshine-roles-permissions\src\FormComponents\RoleFormComponent.php to have role description
    // TODO: Teachers can only see their own students
    // TODO: Simplify role, entry, class assignments
    // TODO: What will happen to general role management? Permissions check general roles.

    protected string $model = User::class;

    protected string $column = 'username';

    protected $guard_name = "moonshine";

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.username'), 'username')
                ->sortable(),

            Text::make(__('admin.name_surname'), 'name')
                ->sortable(),

            Text::make(__('admin.user_title'), 'title')
                ->sortable(),
            
            Email::make(__('admin.email'), 'email')
                ->sortable(),            
        ];
    }

    protected function formFields(): iterable
    {
        $user = auth('moonshine')->user();
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.username'), 'username')
                        ->required()
                        ->placeholder(__('admin.enter_username')),

                    Text::make(__('admin.name_surname'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name_surname')),

                    Text::make(__('admin.user_title'), 'title')
                        ->placeholder(__('admin.enter_user_title')),
                    
                    Email::make(__('admin.email'), 'email')
                        ->required()
                        ->placeholder(__('admin.enter_email')),
                ]),                        
            
                Flex::make([
                    Password::make(__('admin.password'), 'password')
                        ->customAttributes(['autocomplete' => 'new-password'])
                        ->required($this->isCreateFormPage()),

                    PasswordRepeat::make(__('admin.password_repeat'), 'password_confirmation')
                        ->customAttributes(['autocomplete' => 'confirm-password'])
                        ->required($this->isCreateFormPage()),
                ]), 
            ]),
            Box::make([
                RelationRepeater::make(__('admin.organization_assignments'),'organizationUsers', resource: OrganizationUserResource::class)
                    ->removable()
                    ->fields([
                            ID::make(),
                            BelongsTo::make(__('admin.organization'), 'organization', 
                                formatted: fn(Organization $org) => $org->name,
                                resource: OrganizationResource::class),            
                            BelongsTo::make(__('admin.role'), 'role', 
                                formatted: fn(Role $role) => $role->description,
                                resource: RoleResource::class),
                ]),
            ]),
            Box::make([
                RelationRepeater::make(__('admin.class_assignments'),'userClasses', resource: UserClassResource::class)
                    ->removable()
                    ->fields([
                            ID::make(),
                            BelongsTo::make(__('admin.organization'), 'organization', 
                                formatted: fn(Organization $org) => $org->name,
                                resource: OrganizationResource::class),
                            BelongsTo::make(__('admin.class'), 'schoolClass', 
                                formatted: fn(SchoolClass $class) => $class->full_name,
                                resource: SchoolClassResource::class),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.username'), 'username'),
            Text::make(__('admin.name_surname'), 'name'),
            Text::make(__('admin.user_title'), 'title'),
            Email::make(__('admin.email'), 'email'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'username' => ['required', 'string', 'max:255'],
            'name' => ['required', 'string', 'max:255'],
            'title' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $item?->id],
            ...parent::getCommonRules($item),
        ];

        // Password rules
        if (!$item) { // Creating new user
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        } elseif (request()->filled('password')) {
            $rules['password'] = ['string', 'min:8', 'confirmed'];
        }

        return $rules;
    }

    protected function getSearchFields(): array
    {
        return ['username', 'name', 'email'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder    
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0);  // No access if not authenticated
        }

        // System Admin and Admin can see all users
        if ($user->isSchoolAdmin() || $user->isSystemAdmin()) {
            return $builder;
        }

        // Teachers can only see students in their assigned classes
        if ($user->isTeacher()) {
            // Get the teacher's assigned class IDs through user_classes table
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            // Return users who are students in the teacher's classes
            return $builder->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })->whereHas('roles', function ($q) {
                $q->where('name', 'Student');
            });
        }

        // Students have no access to user management
        return $builder->where('id', 0);
    }
}
