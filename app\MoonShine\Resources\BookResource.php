<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\Publisher;
use App\Models\Author;
use App\Models\Role;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Image;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\UI\Components\Badge;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;


#[Icon('book-open')]

class BookResource extends BaseResource
{
    protected string $model = Book::class;

    protected string $column = 'name';

    protected array $with = ['publisher', 'authors', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Image::make(__('admin.cover_image'), 'cover_image'),

            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.isbn'), 'isbn')
                ->sortable(),
            
            BelongsTo::make(__('admin.publisher'), 'publisher', 
                formatted: fn(Publisher $publisher) => $publisher->name)
                ->sortable(),
            
            Text::make(__('admin.author_names'), 'author_names'),
            
            Number::make(__('admin.page_count'), 'page_count')
                ->sortable(),
            
            Number::make(__('admin.year_of_publish'), 'year_of_publish')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                        Text::make(__('admin.name'), 'name')
                            ->required()
                            ->placeholder(__('admin.enter_name')),
                        
                        Flex::make([
                            Text::make(__('admin.isbn'), 'isbn')
                                ->required()
                                ->placeholder(__('admin.enter_isbn')),
                            
                            BelongsTo::make(__('admin.publisher'), 'publisher', 
                                formatted: fn(Publisher $publisher) => $publisher->name,
                                resource: PublisherResource::class)
                                ->required()
                                ->placeholder(__('admin.select_publisher')),
                        ]),
                        
                        Flex::make([
                            Number::make(__('admin.page_count'), 'page_count')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_page_count')),
                            
                            Number::make(__('admin.year_of_publish'), 'year_of_publish')
                                ->required()
                                ->min(1900)
                                ->max(intval(date('Y')))
                                ->placeholder(__('admin.enter_year')),
                        ]),
                        BelongsToMany::make(__('admin.authors'), 'authors', resource: AuthorResource::class, formatted: 'name')
                            ->selectMode(),
                        Image::make(__('admin.cover_image'), 'cover_image'),
                    ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.cover_image'), 'cover_image'),
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.isbn'), 'isbn'),
            Text::make(__('admin.publisher'), 'publisher.name'),
            Number::make(__('admin.page_count'), 'page_count'),
            Number::make(__('admin.year_of_publish'), 'year_of_publish'),
            Text::make(__('admin.difficulty'), 'difficulty'),
            Text::make(__('admin.estimated_reading_time'), 'estimated_reading_time'),
            Text::make(__('admin.publication_info'), 'publication_info'),
            Text::make(__('admin.age'), 'age'),
            Text::make(__('admin.authors'), 'authors'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'isbn' => ['required', 'string', 'max:255', 'unique:books,isbn,' . $item?->id],
            'publisher_id' => ['required', 'exists:publishers,id'],
            'page_count' => ['required', 'integer', 'min:1'],
            'year_of_publish' => ['required', 'integer', 'min:1900', 'max:' . date('Y')],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();
 
        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }
 
    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), [] ) //$this->getIndexFields())
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
//            ->overlay()
            ->header(static fn() => Badge::make('new', 'success'))
            ->title('name')
            ->subtitle('author_names')
            ->url(fn ($book) => $this->getFormPageUrl($book->getKey()))
            ->thumbnail(fn ($book) => asset('storage/' .$book->cover_image))
            ->buttons($this->getIndexButtons());
    }    
}
