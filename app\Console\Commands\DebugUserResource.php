<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserClass;
use Illuminate\Support\Facades\Auth;

class DebugUserResource extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'debug:user-resource';

    /**
     * The console command description.
     */
    protected $description = 'Debug UserResource filtering for teacher';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Debug UserResource Filtering ===');
        $this->newLine();

        // Get teacher
        $teacher = User::whereHas('roles', function($q) {
            $q->where('name', 'Teacher');
        })->first();

        if (!$teacher) {
            $this->error('No teacher found!');
            return 1;
        }

        $this->info("Teacher: {$teacher->name} (ID: {$teacher->id})");
        $this->info("Roles: " . $teacher->roles->pluck('name')->implode(', '));
        $this->newLine();

        // Check teacher's class assignments
        $teacherUserClasses = UserClass::where('user_id', $teacher->id)
            ->where('active', true)
            ->with('schoolClass')
            ->get();

        $this->info("Teacher's Class Assignments:");
        foreach ($teacherUserClasses as $userClass) {
            $this->line("  - Class: " . ($userClass->schoolClass ? $userClass->schoolClass->full_name : 'NULL'));
            $this->line("    Active: " . ($userClass->active ? 'YES' : 'NO'));
        }
        $this->newLine();

        // Get teacher's assigned classes (already done above, use same data)
        $teacherClassIds = $teacherUserClasses->pluck('class_id')->toArray();

        $this->info("Teacher's Class IDs: " . implode(', ', $teacherClassIds));
        $this->newLine();

        if (empty($teacherClassIds)) {
            $this->error('Teacher has no assigned classes!');
            return 1;
        }

        // Find students in teacher's classes
        $studentsInClasses = User::whereHas('userClasses', function ($q) use ($teacherClassIds) {
            $q->where('active', true)
              ->whereIn('class_id', $teacherClassIds);
        })->whereHas('roles', function ($q) {
            $q->where('name', 'Student');
        })->with('roles')->get();

        $this->info("Students in Teacher's Classes:");
        foreach ($studentsInClasses as $student) {
            $this->line("  - {$student->name} (ID: {$student->id}, Roles: " . $student->roles->pluck('name')->implode(', ') . ")");
        }
        $this->newLine();

        $this->info("Total students teacher should see: " . $studentsInClasses->count());

        // Test the actual query from UserResource
        $this->newLine();
        $this->info("=== Testing UserResource Query ===");

        // Login as teacher
        Auth::guard('moonshine')->login($teacher);

        // Simulate the UserResource resolveQuery method
        $query = User::query()->with(['role', 'creator', 'updater']);
        $user = auth('moonshine')->user();
        
        if ($user instanceof User && $user->isTeacher()) {
            $teacherClassIds = UserClass::where('user_id', $user->id)
                ->where('active', true)
                ->pluck('class_id')
                ->filter()
                ->toArray();

            if (!empty($teacherClassIds)) {
                $query->whereHas('userClasses', function ($termQuery) use ($teacherClassIds) {
                    $termQuery->where('active', true)
                      ->whereIn('class_id', $teacherClassIds);
                })->whereHas('roles', function ($roleQuery) {
                    $roleQuery->where('name', 'Student');
                });
            }
        }

        $filteredUsers = $query->get();
        $this->info("UserResource filtered query result: " . $filteredUsers->count() . " users");
        
        foreach ($filteredUsers as $user) {
            $this->line("  - {$user->name} (Role: {$user->role->name})");
        }

        Auth::guard('moonshine')->logout();

        return 0;
    }
}
