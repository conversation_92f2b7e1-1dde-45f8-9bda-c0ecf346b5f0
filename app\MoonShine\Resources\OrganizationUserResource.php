<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\OrganizationUser;
use App\Models\User;
use App\Models\Organization;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('building-office')]
class OrganizationUserResource extends BaseResource
{
    protected string $model = OrganizationUser::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'organization', 'role', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.organization_users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.user'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.role'), 'role', 
                formatted: fn(Role $role) => $role->name,
                resource: RoleResource::class)
                ->sortable(),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.user'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required(),
                
                BelongsTo::make(__('admin.organization'), 'organization', 
                    formatted: fn(Organization $org) => $org->name,
                    resource: OrganizationResource::class)
                    ->required(),
                
                BelongsTo::make(__('admin.role'), 'role', 
                    formatted: fn(Role $role) => $role->name,
                    resource: RoleResource::class)
                    ->required(),
                
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            
            BelongsTo::make(__('admin.user'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class),
            
            BelongsTo::make(__('admin.role'), 'role', 
                formatted: fn(Role $role) => $role->name,
                resource: RoleResource::class),
            
            Switcher::make(__('admin.active'), 'active'),
            
            Text::make(__('admin.summary'), 'summary'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'organization_id' => ['required', 'exists:organizations,id'],
            'role_id' => ['required', 'exists:roles,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['user.name', 'user.email', 'organization.name', 'role.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['created_at' => 'desc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder    
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0);  // No access if not authenticated
        }

        // System Admin can see all organization assignments
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // Admin can see assignments in their organization
        if ($user->isSchoolAdmin()) {
            $userOrganizationIds = $user->activeOrganizationUsers()->pluck('organization_id')->toArray();

            if (empty($userOrganizationIds)) {
                return $builder->where('id', 0); 
            }

            return $builder->whereIn('organization_id', $userOrganizationIds);
        }

        // Teachers and Students have no access to organization user management
        return $builder->where('id', 0); 
    }
}
