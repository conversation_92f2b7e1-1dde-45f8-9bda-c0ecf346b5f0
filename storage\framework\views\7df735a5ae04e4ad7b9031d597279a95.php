<?php $__env->startSection('title', __('student.login_title')); ?>

<?php $__env->startSection('content'); ?>
<div class="game-container flex items-center justify-center min-h-screen">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="achievement-badge bg-game-blue mx-auto mb-4" style="width: 80px; height: 80px; font-size: 32px;">
                🎮
            </div>
            <h1 class="text-3xl font-black text-white mb-2">
                <?php echo e(__('student.welcome_back')); ?>

            </h1>
            <p class="text-white text-lg font-semibold opacity-90">
                <?php echo e(__('student.login_subtitle')); ?>

            </p>
        </div>

        <!-- Login Form -->
        <div class="game-card p-8">
            <form method="POST" action="<?php echo e(route('student.login')); ?>" id="login-form">
                <?php echo csrf_field(); ?>
                
                <!-- Email Field -->
                <div class="mb-6">
                    <label for="username" class="block text-sm font-bold text-gray-700 mb-2">
                        <?php echo e(__('username')); ?>

                    </label>
                    <div class="relative">
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            value="<?php echo e(old('username')); ?>"
                            class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-game-blue focus:outline-none transition-colors text-lg font-semibold <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            placeholder="<?php echo e(__('student.enter_email')); ?>"
                            required
                            autocomplete="username"
                        >
                        <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                            <div class="achievement-badge bg-game-blue w-6 h-6 text-xs">
                                ✉️
                            </div>
                        </div>
                    </div>
                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-500 text-sm font-semibold mt-2"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password Field -->
                <div class="mb-6">
                    <label for="password" class="block text-sm font-bold text-gray-700 mb-2">
                        <?php echo e(__('password')); ?>

                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                            class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-game-blue focus:outline-none transition-colors text-lg font-semibold <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            placeholder="<?php echo e(__('student.enter_password')); ?>"
                            required
                            autocomplete="current-password"
                        >
                        <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                            <div class="achievement-badge bg-game-green w-6 h-6 text-xs">
                                🔒
                            </div>
                        </div>
                    </div>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-500 text-sm font-semibold mt-2"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Remember Me -->
                <div class="mb-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            name="remember" 
                            class="w-5 h-5 text-game-blue border-2 border-gray-300 rounded focus:ring-game-blue focus:ring-2"
                            <?php echo e(old('remember') ? 'checked' : ''); ?>

                        >
                        <span class="ml-3 text-sm font-semibold text-gray-700">
                            <?php echo e(__('student.remember_me')); ?>

                        </span>
                    </label>
                </div>

                <!-- Error Messages -->
                <?php if($errors->any()): ?>
                    <div class="mb-6 p-4 bg-red-50 border-2 border-red-200 rounded-2xl">
                        <div class="flex items-center">
                            <div class="achievement-badge bg-game-red w-8 h-8 text-sm mr-3">
                                ⚠️
                            </div>
                            <div>
                                <h3 class="text-red-800 font-bold text-sm"><?php echo e(__('student.login_error')); ?></h3>
                                <p class="text-red-600 text-sm"><?php echo e(__('student.check_credentials')); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Login Button -->
                <button type="submit" class="game-btn w-full mb-4" id="login-btn">
                    <span id="login-text"><?php echo e(__('student.start_adventure')); ?></span>
                    <div id="login-loading" class="loading-dots hidden ml-2">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                </button>

                <!-- Help Text -->
                <div class="text-center">
                    <p class="text-gray-600 text-sm font-semibold">
                        <?php echo e(__('student.need_help')); ?>

                        <a href="#" class="text-game-blue hover:text-game-purple transition-colors">
                            <?php echo e(__('student.contact_teacher')); ?>

                        </a>
                    </p>
                </div>
            </form>
        </div>

        <!-- Fun Motivational Message -->
        <div class="game-card p-6 mt-6 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200">
            <div class="flex items-center">
                <div class="achievement-badge bg-game-orange mr-4" style="width: 50px; height: 50px; font-size: 20px;">
                    🌟
                </div>
                <div>
                    <h3 class="text-game-blue font-black text-lg"><?php echo e(__('student.daily_motivation')); ?></h3>
                    <p class="text-gray-700 font-semibold text-sm" id="motivation-text">
                        <?php echo e(__('student.motivation_1')); ?>

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Motivational messages
    const motivations = [
        "<?php echo e(__('student.motivation_1')); ?>",
        "<?php echo e(__('student.motivation_2')); ?>",
        "<?php echo e(__('student.motivation_3')); ?>",
        "<?php echo e(__('student.motivation_4')); ?>",
        "<?php echo e(__('student.motivation_5')); ?>"
    ];

    // Rotate motivational messages
    function rotateMotivation() {
        const motivationElement = document.getElementById('motivation-text');
        const randomIndex = Math.floor(Math.random() * motivations.length);
        motivationElement.style.opacity = '0';
        setTimeout(() => {
            motivationElement.textContent = motivations[randomIndex];
            motivationElement.style.opacity = '1';
        }, 300);
    }

    // Handle form submission
    function handleFormSubmission() {
        const form = document.getElementById('login-form');
        const loginBtn = document.getElementById('login-btn');
        const loginText = document.getElementById('login-text');
        const loginLoading = document.getElementById('login-loading');

        form.addEventListener('submit', function(e) {
            // Show loading state
            loginText.textContent = '<?php echo e(__('student.logging_in')); ?>';
            loginLoading.classList.remove('hidden');
            loginBtn.disabled = true;
            
            hapticFeedback('medium');
            showLoading();
        });
    }

    // Add input animations
    function addInputAnimations() {
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                hapticFeedback('light');
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    }

    // Initialize login page
    document.addEventListener('DOMContentLoaded', function() {
        handleFormSubmission();
        addInputAnimations();
        
        // Rotate motivation every 5 seconds
        setInterval(rotateMotivation, 5000);
        
        // Focus on email field
        document.getElementById('username').focus();
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.student', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\resources\views/student/login.blade.php ENDPATH**/ ?>