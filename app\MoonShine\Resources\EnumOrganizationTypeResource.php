<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\EnumOrganizationType;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\Support\Attributes\Icon;

#[Icon('tag')]
class EnumOrganizationTypeResource extends BaseResource
{
    protected string $model = EnumOrganizationType::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.organization_types');
    }

    public function indexFields(): array
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),
        ];
    }

    public function formFields(): array
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
            ]),
        ];
    }

    public function detailFields(): array
    {
        return [
            Text::make(__('admin.name'), 'name'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:enum_organization_types,name,' . $item?->id],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
