<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramTeam;
use App\Models\ProgramTeamMember;
use App\Models\User;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramTeamResource;
use App\MoonShine\Resources\UserResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramTeamMember>
 */
class ProgramTeamMemberResource extends BaseResource
{
    protected string $model = ProgramTeamMember::class;

    protected array $with = ['programTeam', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_team_members');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name)
                ->sortable(),
            Text::make(__('admin.programs'), 'programTeam.program.name')
                ->sortable(),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name)
                ->sortable(),
            Text::make(__('admin.roles'), 'user.roles.0.name'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                    formatted: fn(ProgramTeam $team) => $team->name . ' (' . $team->program->name . ')',
                    resource: ProgramTeamResource::class)
                    ->required()
                    ->placeholder(__('admin.select_team'))
                    ->asyncSearch('name'),
                
                BelongsTo::make(__('admin.users'), 'user',
                    formatted: fn(User $user) => $user->name . ' (' . $user->roles->first()?->name . ')',
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student'))
                    ->asyncSearch('name'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name),
            Text::make(__('admin.programs'), 'programTeam.program.name'),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name),
            Text::make(__('admin.roles'), 'user.roles.0.name'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name,
                resource: ProgramTeamResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_team_id' => ['required', 'exists:program_teams,id'],
            'user_id' => ['required', 'exists:users,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
