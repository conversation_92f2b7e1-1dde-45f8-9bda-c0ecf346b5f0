<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use App\Models\User;

class StudentController extends Controller
{
    /**
     * Show the student login form.
     */
    public function showLogin()
    {
        // If already authenticated as student, redirect to dashboard
        if (Auth::guard('moonshine')->check()) {
            $user = Auth::guard('moonshine')->user();
            if ($user instanceof User && $user->isStudent()) {
                return redirect()->route('student.dashboard');
            }
        }

        return view('student.login');
    }

    /**
     * Handle student login.
     */
    public function login(Request $request): RedirectResponse
    {
        $request->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        $credentials = $request->only('username', 'password');

        if (Auth::guard('moonshine')->attempt($credentials, $request->boolean('remember'))) {
            $user = Auth::guard('moonshine')->user();

            // Check if user is a student
            if ($user instanceof User && $user->isStudent()) {
                $request->session()->regenerate();
                return redirect()->intended(route('student.dashboard'));
            } else {
                Auth::guard('moonshine')->logout();
                return back()->withErrors([
                    'username' => __('admin.student.invalid_credentials'),
                ]);
            }
        }

        return back()->withErrors([
            'username' => __('admin.student.invalid_credentials'),
        ]);
    }

    /**
     * Show the student dashboard.
     */
    public function dashboard(): View
    {
        $user = Auth::guard('moonshine')->user();
        
        return view('student.dashboard', [
            'user' => $user,
        ]);
    }

    /**
     * Handle student logout.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('moonshine')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('student.login');
    }

    /**
     * Show the splash screen.
     */
    public function splash(): View
    {
        return view('student.splash');
    }

    /**
     * Show the offline page.
     */
    public function offline(): View
    {
        return view('student.offline');
    }
}
