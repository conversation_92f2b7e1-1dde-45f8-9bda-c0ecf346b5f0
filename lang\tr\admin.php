<?php

return [
	// System
	'system' => 'Sistem',
	'users' => '<PERSON>llanıcılar',
	'my_students' => 'Öğrencilerim',
	'roles' => 'Roller',
	'admins_title' => 'Yöneticiler',

	// Academic
	'academic' => 'Akademik',
	'organization_types' => 'Kurum Türleri',
	'organization_type' => 'Kurum Türü',
	'organizations' => 'Kurumlar',

	'school_classes' => 'Sınıflar',

	// Books
	'books' => 'Kitaplar',
	'authors' => 'Yazarlar',
	'publishers' => 'Yayınevleri',
	'book_authors' => 'Kitap Yazarları',

	// Gamification
	'gamification' => 'Oyunlaştırma',
	'stories' => 'Hikayeler',
	'story_rules' => 'Hikaye Kuralları',
	'story_rule_details' => 'Hikaye Kural Detayları',
	'story_chapters' => 'Hikaye Bölümleri',
	'story_characters' => 'Hikaye Karakterleri',
	'story_character_stages' => '<PERSON>kt<PERSON> Aşamaları',
	'story_achievements' => 'Hikaye Başarıları',
	'story_books' => 'Hikaye Kitapları',

	// Reading Programs
	'reading_programs' => 'Okuma Programları',
	'programs' => 'Programlar',
	'program_schools' => 'Program Okulları',
	'program_classes' => 'Program Sınıfları',
	'program_books' => 'Program Kitapları',
	'program_teams' => 'Program Takımları',
	'program_team_members' => 'Takım Üyeleri',
	'program_user_levels' => 'Kullanıcı İlerlemesi',
	'program_user_achievements' => 'Kullanıcı Başarıları',
	'program_user_characters' => 'Kullanıcı Karakterleri',
	'program_user_maps' => 'Kullanıcı Haritaları',
	'program_user_points' => 'Kullanıcı Puanları',
	'program_user_books' => 'Kullanıcı Kitap Atamaları',

	// Program Tasks
	'program_tasks' => 'Program Görevleri',
	'program_task_instances' => 'Görev Atamaları',
	'program_task_actions' => 'Görev Eylemleri',

	// Assessment System
	'assessment_system' => 'Değerlendirme Sistemi',
	'book_questions' => 'Kitap Soruları',
	'book_words' => 'Kitap Kelimeleri',
	'book_activity_types' => 'Etkinlik Türleri',
	'program_book_quizzes' => 'Kitap Sınavları',
	'program_book_activities' => 'Kitap Etkinlikleri',

	// Reading Log System
	'reading_log_system' => 'Okuma Günlüğü Sistemi',
	'program_reading_logs' => 'Okuma Günlükleri',

	// Program Fields
	'program_name' => 'Program Adı',
	'program_description' => 'Açıklama',
	'story' => 'Hikaye',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'is_active' => 'Aktif',
	'status' => 'Durum',
	'duration' => 'Süre',
	'participating_schools' => 'Katılımcı Okullar',
	'participating_classes' => 'Katılımcı Sınıflar',
	'associated_books' => 'İlişkili Kitaplar',
	'team_name' => 'Takım Adı',
	'current_chapter' => 'Mevcut Bölüm',
	'current_stage' => 'Mevcut Aşama',
	'earned_at' => 'Kazanıldığı Tarih',
	'points' => 'Puanlar',
	'point_source' => 'Puan Kaynağı',
	'item_type' => 'Öğe Türü',
	'coordinates' => 'Koordinatlar',
	'x_coordinate' => 'X Koordinatı',
	'y_coordinate' => 'Y Koordinatı',

	// Task Fields
	'task_name' => 'Görev Adı',
	'task_description' => 'Görev Açıklaması',
	'task_type' => 'Görev Türü',
	'is_recurring' => 'Tekrarlanan',
	'recurrence_pattern' => 'Tekrarlama Deseni',
	'page_start' => 'Başlangıç Sayfası',
	'page_end' => 'Bitiş Sayfası',
	'page_range' => 'Sayfa Aralığı',
	'assigned_via' => 'Atama Şekli',
	'task_instance' => 'Görev Örneği',
	'action_type' => 'Eylem Türü',
	'action_date' => 'Eylem Tarihi',
	'points_awarded' => 'Verilen Puanlar',
	'completed_by' => 'Tamamlayan',
	'action_summary' => 'Eylem Özeti',
	'days_remaining' => 'Kalan Günler',
	'scheduling' => 'Zamanlama',
	'book_settings' => 'Kitap Ayarları',

	// Assessment Fields
	'question_text' => 'Soru Metni',
	'correct_answer' => 'Doğru Cevap',
	'incorrect_answer' => 'Yanlış Cevap',
	'question_image_url' => 'Soru Resim URL\'si',
	'question_audio_url' => 'Soru Ses URL\'si',
	'question_video_url' => 'Soru Video URL\'si',
	'page_reference' => 'Sayfa Referansı',
	'word' => 'Kelime',
	'definition' => 'Tanım',
	'synonym' => 'Eş Anlamlı',
	'antonym' => 'Zıt Anlamlı',
	'word_display' => 'Kelime Görünümü',
	'category' => 'Kategori',
	'min_word_count' => 'Minimum Kelime Sayısı',
	'max_word_count' => 'Maksimum Kelime Sayısı',
	'word_count_range' => 'Kelime Sayısı Aralığı',
	'points_base' => 'Temel Puanlar',
	'activity_type' => 'Etkinlik Türü',
	'activity_content' => 'Etkinlik İçeriği',
	'word_count' => 'Kelime Sayısı',
	'quiz_type' => 'Sınav Türü',
	'total_questions' => 'Toplam Soru Sayısı',
	'correct_answers' => 'Doğru Cevaplar',
	'score_percentage' => 'Puan Yüzdesi',
	'passing_score' => 'Geçme Puanı',
	'is_passed' => 'Geçti',
	'attempt_number' => 'Deneme Sayısı',
	'time_limit_minutes' => 'Süre Sınırı (Dakika)',
	'duration_minutes' => 'Süre (Dakika)',
	'reviewed_by' => 'İnceleyen',
	'reviewed_at' => 'İnceleme Tarihi',
	'feedback' => 'Geri Bildirim',
	'is_reviewed' => 'İncelendi',
	'media_type' => 'Medya Türü',
	'answer_options' => 'Cevap Seçenekleri',
	'media_content' => 'Medya İçeriği',
	'instructions' => 'Talimatlar',

	// Reading Log Fields
	'reading_date' => 'Okuma Tarihi',
	'start_page' => 'Başlangıç Sayfası',
	'end_page' => 'Bitiş Sayfası',
	'page_range' => 'Sayfa Aralığı',
	'pages_read' => 'Okunan Sayfa Sayısı',
	'reading_duration_minutes' => 'Okuma Süresi (Dakika)',
	'reading_speed' => 'Okuma Hızı (Sayfa/Dakika)',
	'reading_notes' => 'Okuma Notları',
	'is_verified' => 'Doğrulandı',
	'verified_by' => 'Doğrulayan',
	'verified_at' => 'Doğrulama Tarihi',
	'points_awarded' => 'Verilen Puanlar',
	'reading_details' => 'Okuma Detayları',
	'verification' => 'Doğrulama',
	'reading_streak' => 'Okuma Serisi',
	'total_sessions' => 'Toplam Oturum',
	'average_pages_per_session' => 'Oturum Başına Ortalama Sayfa',
	'completion_percentage' => 'Tamamlanma Yüzdesi',

	// Program Status
	'active' => 'Aktif',
	'inactive' => 'Pasif',
	'upcoming' => 'Yaklaşan',
	'completed' => 'Tamamlanmış',

	// Point Sources
	'achievement' => 'Başarı',
	'task' => 'Görev',
	'quest' => 'Macera',
	'reading' => 'Okuma',
	'bonus' => 'Bonus',

	// Task Types
	'reading_log' => 'Okuma Günlüğü',
	'activity' => 'Etkinlik',
	'question' => 'Soru',
	'physical' => 'Fiziksel',

	// Task Status
	'pending' => 'Beklemede',
	'missed' => 'Kaçırıldı',
	'excused' => 'Mazur Görüldü',
	'reassigned' => 'Yeniden Atandı',
	'upcoming' => 'Yaklaşan',
	'expired' => 'Süresi Dolmuş',

	// Recurrence Patterns
	'daily' => 'Günlük',
	'weekly' => 'Haftalık',
	'one_time' => 'Tek Seferlik',

	// Assignment Types
	'individual' => 'Bireysel',
	'team' => 'Takım',

	// Activity Categories
	'vocabulary' => 'Kelime Bilgisi',
	'spelling' => 'Yazım',
	'writing' => 'Yazma',
	'comprehension' => 'Anlama',
	'creative' => 'Yaratıcı',

	// Quiz Types
	'completion' => 'Kitap Tamamlama Sınavı',
	'daily_reading' => 'Günlük Okuma Sınavı',
	'practice' => 'Pratik Sınavı',

	// Difficulty Levels
	'easy' => 'Kolay',
	'medium' => 'Orta',
	'hard' => 'Zor',

	// Item Types
	'item' => 'Öğe',
	'badge' => 'Rozet',
	'reward' => 'Ödül',
	'trophy' => 'Kupa',
	'collectible' => 'Koleksiyon',

	// Common Actions
	'select_program' => 'Program Seç',
	'select_story' => 'Hikaye Seç',
	'select_student' => 'Öğrenci Seç',
	'select_school' => 'Okul Seç',
	'select_chapter' => 'Bölüm Seç',
	'select_achievement' => 'Başarı Seç',
	'enter_name' => 'Ad Girin',
	'enter_name_surname' => 'Ad & Soyad Girin',
	'enter_description' => 'Açıklama Girin',
	'enter_team_name' => 'Takım Adı Girin',
	'member_count' => 'Üye Sayısı',
	'achievement_type' => 'Başarı Türü',
	'select_class' => 'Sınıf Seç',
	'select_book' => 'Kitap Seç',
	'select_team' => 'Takım Seç',
	'select_character' => 'Karakter Seç',
	'item_id' => 'Öğe ID',
	'isbn' => 'ISBN',

	// Additional common fields
	'title' => 'Başlık',
	'enter_title' => 'Başlık Girin',
	'enter_user_title' => 'Ünvan Girin',
	'enter_email' => 'E-posta Girin',
	'select_role' => 'Rol Seç',
	'select_user' => 'Kullanıcı Seç',
	'main_information' => 'Ana Bilgiler',
	'audit_info' => 'Denetim Bilgileri',
	'user' => 'Kullanıcı',
	'role' => 'Rol',
	'email' => 'E-posta',
	'username' => 'Kullanıcı Adı',
	'enter_username' => 'Kullanıcı Adını Girin',

	// Tags
	'tags' => 'Etiketler',
	'tag_values' => 'Etiket Değerleri',
	
	// Common Fields
	'id' => 'ID',
	'name' => 'Ad',
	'name_surname' => 'Ad & Soyad',
	'user_title' => 'Unvan',
	'email' => 'E-posta',
	'password' => 'Şifre',
	'password_repeat' => 'Şifre Tekrar',
	'description' => 'Açıklama',
	'active' => 'Aktif',
	'level' => 'Seviye',
	'type' => 'Tip',
	'parent' => 'Üst',
	'children' => 'Alt Kurumlar',
	'role' => 'Rol',
	'user' => 'Kullanıcı',
	'organization' => 'Kurum',
	'class' => 'Sınıf',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'status' => 'Durum',
	'created_at' => 'Oluşturulma Tarihi',
	'updated_at' => 'Güncellenme Tarihi',
	'created_by' => 'Oluşturan',
	'updated_by' => 'Güncelleyen',
	
	// Book Fields
	'isbn' => 'ISBN',
	'publisher' => 'Yayınevi',
	'page_count' => 'Sayfa Sayısı',
	'year_of_publish' => 'Yayın Yılı',
	'author' => 'Yazar',
	'book' => 'Kitap',

	// Gamification Fields
	'story' => 'Hikaye',
	'title' => 'Başlık',
	'cover_image' => 'Kapak Resmi',
	'map_grid_rows' => 'Harita Satır Sayısı',
	'map_grid_columns' => 'Harita Sütun Sayısı',
	'map_background_image' => 'Harita Arkaplan Resmi',
	'rule_type' => 'Kural Tipi',
	'quantity' => 'Miktar',
	'required_type' => 'Gerekli Tip',
	'required_id' => 'Gerekli ID',
	'sequence' => 'Sıra',
	'sequence_hint' => 'Hikaydeki kitapların sırası (0 = otomatik olarak sonraki sırayı ata)',
	'book_already_in_story' => 'Bu kitap zaten bu hikaye ile ilişkilendirilmiş',
	'sequence_already_exists' => 'Bu sıra numarası bu hikaye için zaten mevcut',
	'is_first' => 'İlk mi',
	'is_last' => 'Son mu',
	'yes' => 'Evet',
	'no' => 'Hayır',
	'assignment_info' => 'Atama Bilgileri',
	'assignment_date_hint' => 'Kitabın öğrenciye atandığı tarih',
	'completion_date_hint' => 'Öğrencinin kitabı okumayı tamamladığı tarih (devam ediyorsa boş bırakın)',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'status' => 'Durum',
	'completed' => 'Tamamlandı',
	'overdue' => 'Gecikmiş',
	'ongoing' => 'Devam Ediyor',
	'unknown' => 'Bilinmiyor',
	'duration_days' => 'Süre',
	'days' => 'gün',
	'days_ongoing' => 'gün (devam ediyor)',
	'is_completed' => 'Tamamlandı mı',
	'is_overdue' => 'Gecikmiş mi',
	'must_be_student' => 'Seçilen kullanıcı öğrenci olmalıdır',
	'book_already_assigned' => 'Bu kitap bu programda bu öğrenciye zaten atanmış',
	'student_email' => 'Öğrenci E-postası',
	'student_class' => 'Öğrenci Sınıfı',
	'student_organization' => 'Öğrenci Kurumu',
	'dates' => 'Tarihler',
	'unlock_rule' => 'Açma Kuralı',
	'map_start_x' => 'Harita Başlangıç X',
	'map_start_y' => 'Harita Başlangıç Y',
	'map_end_x' => 'Harita Bitiş X',
	'map_end_y' => 'Harita Bitiş Y',
	'base_image' => 'Temel Resim',
	'character' => 'Karakter',
	'stage_number' => 'Aşama Numarası',
	'image' => 'Resim',
	'abilities' => 'Yetenekler',
	'achievement' => 'Başarı',
	'is_dynamic_position' => 'Dinamik Pozisyon',
	'chapter' => 'Bölüm',
	'stage' => 'Aşama',
	'rule' => 'Kural',
	'map_coordinates' => 'Harita Koordinatları',
	'map_dimensions' => 'Harita Boyutları',
	'chapter_count' => 'Bölüm Sayısı',
	'character_count' => 'Karakter Sayısı',
	'achievement_count' => 'Başarı Sayısı',
	'stage_count' => 'Aşama Sayısı',
	'progression_summary' => 'İlerleme Özeti',
	'full_title' => 'Tam Başlık',
	'full_name' => 'Tam Ad',
	'abilities_text' => 'Yetenekler Metni',
	'type_display_name' => 'Tip Görünen Adı',
	'info' => 'Bilgi',
	'required_item_name' => 'Gerekli Öğe Adı',
	
	// Counts
	'users_count' => 'Kullanıcı Sayısı',
	'books_count' => 'Kitap Sayısı',
	'student_count' => 'Öğrenci Sayısı',
	'duration_days' => 'Süre (Gün)',
	
	// Relationships
	'relationships' => 'İlişkiler',
	'organization_assignments' => 'Kurum/Rol',
	'class_assignments' => 'Sınıflar',
	'assignment_info' => 'Atama Bilgileri',
	'summary' => 'Özet',
	
	// Info Sections
	'main_information' => 'Ana Bilgiler',
	'audit_info' => 'Denetim Bilgileri',
	'user_info' => 'Kullanıcı Bilgileri',
	'role_info' => 'Rol Bilgileri',
	'organization_info' => 'Kurum Bilgileri',
	'book_info' => 'Kitap Bilgileri',
	'author_info' => 'Yazar Bilgileri',
	'publisher_info' => 'Yayınevi Bilgileri',
	'class_info' => 'Sınıf Bilgileri',
	'grade_level_info' => 'Sınıf Seviyesi Bilgileri',
	'assignments' => 'Atamalar',
	
	// Placeholders
	'enter_name' => 'Ad girin',
	'enter_title' => 'Unvan girin',
	'enter_email' => 'E-posta girin',
	'enter_description' => 'Açıklama girin',
	'select_role' => 'Rol seçin',
	'select_level' => 'Seviye seçin',
	'select_type' => 'Tip seçin',
	'select_parent' => 'Üst kurum seçin',
	'select_user' => 'Kullanıcı seçin',
	'select_organization' => 'Kurum seçin',
	'select_organization_type' => 'Kurum türü seçin',
	'select_class' => 'Sınıf seçin',
	'select_publisher' => 'Yayınevi seçin',
	'select_author' => 'Yazar seçin',
	'select_book' => 'Kitap seçin',
	'select_grade_level' => 'Sınıf seviyesi seçin',
	'enter_isbn' => 'ISBN girin',
	'enter_page_count' => 'Sayfa sayısı girin',
	'enter_year' => 'Yıl girin',
	'enter_title' => 'Başlık girin',
	'select_story' => 'Hikaye seçin',
	'select_rule_type' => 'Kural tipi seçin',
	'select_required_type' => 'Gerekli tip seçin',
	'enter_quantity' => 'Miktar girin',
	'enter_sequence' => 'Sıra girin',
	'select_unlock_rule' => 'Açma kuralı seçin',
	'select_character' => 'Karakter seçin',
	'enter_stage_number' => 'Aşama numarası girin',
	'select_achievement_type' => 'Başarı tipi seçin',

	// Task Placeholders
	'enter_task_name' => 'Görev adı girin',
	'enter_task_description' => 'Görev açıklaması girin',
	'select_task_type' => 'Görev türü seçin',
	'select_recurrence_pattern' => 'Tekrarlama deseni seçin',
	'enter_page_start' => 'Başlangıç sayfası girin',
	'enter_page_end' => 'Bitiş sayfası girin',
	'enter_points_awarded' => 'Verilen puanları girin',
	'select_task' => 'Görev seçin',
	'select_student' => 'Öğrenci seçin',
	'select_task_instance' => 'Görev örneği seçin',
	'select_action_type' => 'Eylem türü seçin',
	'select_completed_by' => 'Tamamlayan seçin',
	'enter_notes' => 'Notlar girin',

	// Assessment Placeholders
	'enter_question_text' => 'Soru metni girin',
	'enter_correct_answer' => 'Doğru cevap girin',
	'enter_incorrect_answer' => 'Yanlış cevap girin',
	'enter_image_url' => 'Resim URL\'si girin',
	'enter_audio_url' => 'Ses URL\'si girin',
	'enter_video_url' => 'Video URL\'si girin',
	'enter_word' => 'Kelime girin',
	'enter_definition' => 'Tanım girin',
	'enter_synonym' => 'Eş anlamlı girin',
	'enter_antonym' => 'Zıt anlamlı girin',
	'enter_page_reference' => 'Sayfa referansı girin',
	'select_category' => 'Kategori seçin',
	'enter_activity_name' => 'Etkinlik adı girin',
	'enter_activity_description' => 'Etkinlik açıklaması girin',
	'enter_min_word_count' => 'Minimum kelime sayısı girin',
	'enter_max_word_count' => 'Maksimum kelime sayısı girin',
	'enter_base_points' => 'Temel puanları girin',
	'select_quiz_type' => 'Sınav türü seçin',
	'enter_total_questions' => 'Toplam soru sayısı girin',
	'enter_passing_score' => 'Geçme puanı girin',
	'enter_time_limit' => 'Süre sınırı girin',
	'select_activity_type' => 'Etkinlik türü seçin',
	'enter_activity_content' => 'Etkinlik içeriği girin',
	'enter_word_count' => 'Kelime sayısı girin',
	'enter_points_earned' => 'Kazanılan puanları girin',
	'select_reviewer' => 'İnceleyen seçin',
	'enter_feedback' => 'Geri bildirim girin',
	'select_task_instance' => 'Görev örneği seçin',

	// Reading Log Placeholders
	'select_reading_date' => 'Okuma tarihi seçin',
	'enter_start_page' => 'Başlangıç sayfası girin',
	'enter_end_page' => 'Bitiş sayfası girin',
	'enter_reading_duration' => 'Okuma süresini dakika olarak girin',
	'enter_reading_notes' => 'Okuma notları veya düşünceler girin',
	'select_verifier' => 'Doğrulayıcı seçin',
	'enter_points_awarded' => 'Verilen puanları girin',

	// Reading Log Messages
	'reading_log_already_exists_for_date' => 'Bu tarih için okuma günlüğü zaten mevcut',
	'reading_log_created_successfully' => 'Okuma günlüğü başarıyla oluşturuldu',
	'reading_log_verified_successfully' => 'Okuma günlüğü başarıyla doğrulandı',
	'bulk_verification_completed' => 'Toplu doğrulama tamamlandı',

	// Attributes
	'hierarchy_path' => 'Hiyerarşi Yolu',
	'role_level' => 'Rol Seviyesi',
	'full_name' => 'Tam Ad',
	'display_name' => 'Görünen Ad',
	'publication_info' => 'Yayın Bilgisi',
	'author_names' => 'Yazar Adları',
	'difficulty' => 'Zorluk',
	'estimated_reading_time' => 'Tahmini Okuma Süresi',
	'age' => 'Yaş',
	'usage_count' => 'Kullanım Sayısı',
	'tag_type' => 'Etiket Tipi',
	'taggable_type' => 'Etiketlenen Tip',
	'formatted_name' => 'Biçimlendirilmiş Ad',
	'total_pages' => 'Toplam Sayfa',
	'average_pages' => 'Ortalama Sayfa',
	'books_list' => 'Kitap Listesi',
	
	// Privacy Agreement
	'privacy_agreement_title' => 'Gizlilik Politikası Sözleşmesi',
	'privacy_policy_title' => 'Gizlilik Politikası',
	'privacy_agreement_consent' => 'Gizlilik politikasını ve kullanım şartlarını kabul ediyorum',
	'accept_and_continue' => 'Kabul Et ve Devam Et',
	'privacy_consent_required' => 'Devam etmek için gizlilik politikasını kabul etmelisiniz.',
	'privacy_agreement_accepted' => 'Gizlilik politikası başarıyla kabul edildi.',
	'privacy_agreement_error' => 'Onayınız işlenirken bir hata oluştu. Lütfen tekrar deneyin.',
	'authentication_required' => 'Kimlik doğrulama gerekli.',
	'last_updated' => 'Son Güncelleme',
	'version' => 'Versiyon',

	// Privacy Policy Content
	'privacy_policy_intro' => 'Bu gizlilik politikası, okuma takip uygulamamızı kullandığınızda kişisel bilgilerinizi nasıl topladığımızı, kullandığımızı ve koruduğumuzu açıklar.',
	'data_collection_title' => '1. Veri Toplama',
	'data_collection_content' => 'Adınız, e-posta adresiniz, okuma ilerlemeniz ve hizmetlerimizi sağlamak ve geliştirmek için kullanım verileriniz gibi doğrudan bize sağladığınız bilgileri topluyoruz.',
	'data_usage_title' => '2. Veri Kullanımı',
	'data_usage_content' => 'Bilgilerinizi eğitim hizmetleri sağlamak, okuma ilerlemesini takip etmek, raporlar oluşturmak ve uygulama işlevselliğimizi geliştirmek için kullanıyoruz.',
	'data_sharing_title' => '3. Veri Paylaşımı',
	'data_sharing_content' => 'Hizmetlerimizi sağlamak için gerekli olan durumlar veya yasal zorunluluklar dışında kişisel bilgilerinizi üçüncü taraflarla satmıyor veya paylaşmıyoruz.',
	'data_security_title' => '4. Veri Güvenliği',
	'data_security_content' => 'Kişisel bilgilerinizi yetkisiz erişim, değişiklik, ifşa veya imhaya karşı korumak için uygun güvenlik önlemleri uyguluyoruz.',
	'user_rights_title' => '5. Haklarınız',
	'user_rights_content' => 'Kişisel bilgilerinize erişme, güncelleme veya silme hakkınız vardır. Bu hakları kullanmak istiyorsanız bizimle iletişime geçin.',
	'contact_info_title' => '6. İletişim Bilgileri',
	'contact_info_content' => 'Bu gizlilik politikası hakkında sorularınız varsa, lütfen sistem yöneticiniz veya okul yönetimiyle iletişime geçin.',

	// User Agreements
	'user_agreements' => 'Kullanıcı Sözleşmeleri',
	'agreement_type' => 'Sözleşme Türü',
	'accepted_at' => 'Kabul Tarihi',
	'ip_address' => 'IP Adresi',
	'privacy_policy' => 'Gizlilik Politikası',
	'terms_of_service' => 'Hizmet Şartları',
	'cookie_policy' => 'Çerez Politikası',
	'enter_version' => 'Versiyon girin',
	'enter_ip_address' => 'IP adresi girin',

	// Actions
	'create' => 'Oluştur',
	'edit' => 'Düzenle',
	'cancel' => 'İptal',
	'delete' => 'Sil',
	'view' => 'Görüntüle',
	'save' => 'Kaydet',
	'cancel' => 'İptal',
	'activate' => 'Aktifleştir',
	'deactivate' => 'Pasifleştir',
	
	// Messages
	'no_records' => 'Kayıt bulunamadı',
	'loading' => 'Yükleniyor...',
	'saved_successfully' => 'Başarıyla kaydedildi',
	'deleted_successfully' => 'Başarıyla silindi',
	'error_occurred' => 'Bir hata oluştu',
	
	// Validation
	'required' => 'Bu alan zorunludur',
	'email_invalid' => 'Geçerli bir e-posta adresi girin',
	'password_min' => 'Şifre en az 8 karakter olmalıdır',
	'password_confirmed' => 'Şifreler eşleşmiyor',
	'unique' => 'Bu değer zaten kullanılıyor',
	'exists' => 'Seçilen değer geçerli değil',
	
	// Role Levels
	'system_admin' => 'Sistem Yöneticisi',
	'school_admin' => 'Kurum Yöneticisi',
	'teacher' => 'Öğretmen',
	'student' => 'Öğrenci',
	
	// Organization Types
	'school_group' => 'Okul Grubu',
	'school' => 'Okul',
	
	// Tag Types
	'organization_tags' => 'Kurum Etiketleri',
	'book_tags' => 'Kitap Etiketleri',
	'user_tags' => 'Kullanıcı Etiketleri',
	
	// Status Values
	'current' => 'Güncel',
	'future' => 'Gelecek',
	'past' => 'Geçmiş',
	'easy' => 'Kolay',
	'medium' => 'Orta',
	'hard' => 'Zor',
	'very_hard' => 'Çok Zor',

	// Rule Types
	'points' => 'Puanlar',
	'achievement_count' => 'Başarı Sayısı',
	'book_count' => 'Kitap Sayısı',
	'specific_achievements' => 'Belirli Başarılar',
	'specific_books' => 'Belirli Kitaplar',
	'chapter_completion' => 'Bölüm Tamamlama',
	'time_based' => 'Zaman Bazlı',

	// Required Types
	'required_achievement' => 'Gerekli Başarı',
	'required_book' => 'Gerekli Kitap',
	'required_chapter' => 'Gerekli Bölüm',
	'required_character_stage' => 'Gerekli Karakter Aşaması',

	// Achievement Types
	'item' => 'Eşya',
	'badge' => 'Rozet',
	'reward' => 'Ödül',
	'trophy' => 'Kupa',
	'collectible' => 'Koleksiyon',
	
	// Time
	'hours' => 'saat',
	'minutes' => 'dakika',
	'days' => 'gün',
	'years' => 'yıl',
];
