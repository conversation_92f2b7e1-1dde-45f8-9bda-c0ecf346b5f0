<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\SchoolClass;
use App\Models\Organization;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;

#[Icon('user-group')]

class SchoolClassResource extends BaseResource
{
//    use WithRolePermissions;

    protected string $model = SchoolClass::class;

    protected string $column = 'name';

    protected array $with = ['organization', 'gradeLevel', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.school_classes');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),            
            
            BelongsTo::make(__('admin.organization'), 'organization',
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class)
                ->sortable(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Number::make(__('admin.student_count'), 'student_count'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([                
                Flex::make([
                    BelongsTo::make(__('admin.organization'), 'organization',
                        formatted: fn(Organization $org) => $org->name,
                        resource: OrganizationResource::class)
                        ->required()
                        ->placeholder(__('admin.select_organization'))
                        ->valuesQuery(function ($query) {
                            return $query->where('active', true)->where('org_type', Organization::TYPE_SCHOOL);
                        }),

                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                ]),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
                BelongsTo::make(__('admin.organization'), 'organization',
                    formatted: fn(Organization $org) => $org->name,resource: OrganizationResource::class),

                Text::make(__('admin.name'), 'name'),
                
                Number::make(__('admin.student_count'), 'student_count'),
                HasMany::make(__('admin.users'), 'users', 
                    resource: UserResource::class),
                ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'organization_id' => ['required', 'exists:organizations,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['organization_id' => 'asc', 'name' => 'asc'];
    }
}
