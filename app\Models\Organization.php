<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Organization extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'org_type',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'org_type' => 'integer',
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Organization type constants.
     */
    const TYPE_SCHOOL_GROUP = 1;
    const TYPE_SCHOOL = 2;

    /**
     * Get all organization types.
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_SCHOOL_GROUP => 'School Group',
            self::TYPE_SCHOOL => 'School',
        ];
    }

    /**
     * Get organization type name.
     */
    public function getTypeNameAttribute(): string
    {
        return self::getTypes()[$this->org_type] ?? 'Unknown';
    }





    /**
     * Get school classes for this organization.
     */
    public function schoolClasses(): Has<PERSON>any
    {
        return $this->hasMany(SchoolClass::class);
    }

    /**
     * Get organization user assignments for this organization.
     */
    public function organizationUsers(): HasMany
    {
        return $this->hasMany(OrganizationUser::class);
    }

    /**
     * Get active organization user assignments for this organization.
     */
    public function activeOrganizationUsers(): HasMany
    {
        return $this->hasMany(OrganizationUser::class)->where('active', true);
    }

    /**
     * Get user class assignments for this organization.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class);
    }

    /**
     * Get active user class assignments for this organization.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class)->where('active', true);
    }

    /**
     * Get users assigned to this organization.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->withTimestamps();
    }

    /**
     * Get active users assigned to this organization.
     */
    public function activeUsers()
    {
        return $this->belongsToMany(User::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->wherePivot('active', true)
                    ->withTimestamps();
    }

    /**
     * Get tag values for this organization.
     */
    public function tagValues(): HasMany
    {
        return $this->hasMany(TagValue::class, 'taggable_id')
                    ->where('taggable_type', TagValue::TYPE_ORGANIZATION);
    }

    /**
     * Scope to get active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get organizations by type.
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('org_type', $type);
    }

    /**
     * Scope to get school groups.
     */
    public function scopeSchoolGroups($query)
    {
        return $query->where('org_type', self::TYPE_SCHOOL_GROUP);
    }

    /**
     * Scope to get schools.
     */
    public function scopeSchools($query)
    {
        return $query->where('org_type', self::TYPE_SCHOOL);
    }

    /**
     * Scope to get root organizations (no parent).
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Check if this is a school group.
     */
    public function isSchoolGroup(): bool
    {
        return $this->org_type === self::TYPE_SCHOOL_GROUP;
    }

    /**
     * Check if this is a school.
     */
    public function isSchool(): bool
    {
        return $this->org_type === self::TYPE_SCHOOL;
    }


}
