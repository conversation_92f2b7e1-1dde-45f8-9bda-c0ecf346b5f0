<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Organization extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'organization_type_id',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the organization type.
     */
    public function organizationType(): BelongsTo
    {
        return $this->belongsTo(EnumOrganizationType::class, 'organization_type_id');
    }

    /**
     * Get school classes for this organization.
     */
    public function schoolClasses(): HasMany
    {
        return $this->hasMany(SchoolClass::class);
    }

    /**
     * Get organization user assignments for this organization.
     */
    public function organizationUsers(): HasMany
    {
        return $this->hasMany(OrganizationUser::class);
    }

    /**
     * Get active organization user assignments for this organization.
     */
    public function activeOrganizationUsers(): Has<PERSON>any
    {
        return $this->hasMany(OrganizationUser::class)->where('active', true);
    }

    /**
     * Get user class assignments for this organization.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class);
    }

    /**
     * Get active user class assignments for this organization.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class)->where('active', true);
    }

    /**
     * Get users assigned to this organization.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->withTimestamps();
    }

    /**
     * Get active users assigned to this organization.
     */
    public function activeUsers()
    {
        return $this->belongsToMany(User::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->wherePivot('active', true)
                    ->withTimestamps();
    }


    /**
     * Scope to get active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

}
