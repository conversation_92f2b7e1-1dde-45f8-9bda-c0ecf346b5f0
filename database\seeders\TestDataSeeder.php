<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Term;
use App\Models\Organization;
use App\Models\GradeLevel;
use App\Models\SchoolClass;
use App\Models\User;
use App\Models\Role;
use App\Models\TermUser;
use App\Models\Author;
use App\Models\Publisher;
use App\Models\Book;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test term
        $term = Term::create([
            'name' => '2024-2025 Güz Dönemi',
            'start_date' => '2024-09-01',
            'end_date' => '2025-01-31',
            'active' => true,
        ]);

        // Create grade levels
        $gradeLevels = [];
        for ($i = 1; $i <= 12; $i++) {
            $gradeLevels[] = GradeLevel::create(['name' => (string)$i]);
        }
        $gradeLevels[] = GradeLevel::create(['name' => 'Hazırlık']);

        // Create a school group
        $schoolGroup = Organization::create([
            'name' => 'Örnek Eğitim Grubu',
            'org_type' => Organization::TYPE_SCHOOL_GROUP,
            'active' => true,
        ]);

        // Create schools under the group
        $school1 = Organization::create([
            'name' => 'Örnek İlkokulu',
            'org_type' => Organization::TYPE_SCHOOL,
            'parent_id' => $schoolGroup->id,
            'active' => true,
        ]);

        $school2 = Organization::create([
            'name' => 'Örnek Ortaokulu',
            'org_type' => Organization::TYPE_SCHOOL,
            'parent_id' => $schoolGroup->id,
            'active' => true,
        ]);

        // Assign grade levels to schools
        $school1->gradeLevels()->attach([1, 2, 3, 4]); // İlkokul: 1-4. sınıflar
        $school2->gradeLevels()->attach([5, 6, 7, 8]); // Ortaokul: 5-8. sınıflar

        // Create classes
        foreach ([1, 2, 3, 4] as $gradeId) {
            foreach (['A', 'B'] as $section) {
                SchoolClass::create([
                    'name' => $section,
                    'organization_id' => $school1->id,
                    'grade_level_id' => $gradeId,
                ]);
            }
        }

        foreach ([5, 6, 7, 8] as $gradeId) {
            foreach (['A', 'B', 'C'] as $section) {
                SchoolClass::create([
                    'name' => $section,
                    'organization_id' => $school2->id,
                    'grade_level_id' => $gradeId,
                ]);
            }
        }

        // Create test users
        $roles = Role::all()->keyBy('level');

        // School admin
        $schoolAdmin = User::create([
            'name' => 'Okul Müdürü',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $roles[Role::LEVEL_SCHOOL_ADMIN]->id,
        ]);

        // Teachers
        $teacher1 = User::create([
            'name' => 'Öğretmen Ayşe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $roles[Role::LEVEL_TEACHER]->id,
        ]);

        $teacher2 = User::create([
            'name' => 'Öğretmen Mehmet',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $roles[Role::LEVEL_TEACHER]->id,
        ]);

        // Students
        for ($i = 1; $i <= 10; $i++) {
            User::create([
                'name' => "Öğrenci $i",
                'email' => "ogrenci$<EMAIL>",
                'password' => Hash::make('password'),
                'role_id' => $roles[Role::LEVEL_STUDENT]->id,
            ]);
        }

        // Create term assignments
        $class1A = SchoolClass::where('name', 'A')->where('grade_level_id', 1)->first();
        $class2A = SchoolClass::where('name', 'A')->where('grade_level_id', 2)->first();

        // Create some sample books
        $publisher1 = Publisher::create(['name' => 'Yapı Kredi Yayınları']);
        $publisher2 = Publisher::create(['name' => 'İş Bankası Kültür Yayınları']);

        $author1 = Author::create(['name' => 'Nazim Hikmet']);
        $author2 = Author::create(['name' => 'Yaşar Kemal']);
        $author3 = Author::create(['name' => 'Orhan Pamuk']);

        $book1 = Book::create([
            'name' => 'Memleketimden İnsan Manzaraları',
            'isbn' => '*************',
            'publisher_id' => $publisher1->id,
            'page_count' => 320,
            'year_of_publish' => 2019,
        ]);

        $book2 = Book::create([
            'name' => 'İnce Memed',
            'isbn' => '*************',
            'publisher_id' => $publisher2->id,
            'page_count' => 456,
            'year_of_publish' => 2018,
        ]);

        $book3 = Book::create([
            'name' => 'Kar',
            'isbn' => '*************',
            'publisher_id' => $publisher1->id,
            'page_count' => 512,
            'year_of_publish' => 2020,
        ]);

        // Assign authors to books
        $book1->authors()->attach($author1->id);
        $book2->authors()->attach($author2->id);
        $book3->authors()->attach($author3->id);
    }
}
