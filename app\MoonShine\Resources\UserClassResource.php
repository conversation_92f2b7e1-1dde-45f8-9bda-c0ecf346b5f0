<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserClass;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Organization;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('academic-cap')]
class UserClassResource extends BaseResource
{
    // TODO: Class list should be filtered by organization
    // TODO: Organization list should be filtered by user's active organizations

    protected string $model = UserClass::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'schoolClass', 'organization', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.user_classes');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.user'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.class'), 'schoolClass', 
                formatted: fn(SchoolClass $class) => $class->full_name,
                resource: SchoolClassResource::class)
                ->sortable(),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.user'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required(),
                
                BelongsTo::make(__('admin.organization'), 'organization', 
                    formatted: fn(Organization $org) => $org->name,
                    resource: OrganizationResource::class)
                    ->required(),

                BelongsTo::make(__('admin.class'), 'schoolClass', 
                    formatted: fn(SchoolClass $class) => $class->full_name,
                    resource: SchoolClassResource::class)
                    ->required(),                
                
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            
            BelongsTo::make(__('admin.user'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class),
            
            BelongsTo::make(__('admin.class'), 'schoolClass', 
                formatted: fn(SchoolClass $class) => $class->full_name,
                resource: SchoolClassResource::class),
            
            Switcher::make(__('admin.active'), 'active'),
            
            Text::make(__('admin.summary'), 'summary'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'class_id' => ['required', 'exists:school_classes,id'],
            'organization_id' => ['required', 'exists:organizations,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['user.name', 'user.email', 'schoolClass.full_name', 'organization.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['created_at' => 'desc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder    
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all class assignments
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // Admin can see assignments in their organization
        if ($user->isSchoolAdmin()) {
            $userOrganizationIds = $user->activeOrganizationUsers()->pluck('organization_id')->toArray();

            if (empty($userOrganizationIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereIn('organization_id', $userOrganizationIds);
        }

        // Teachers can see assignments in their classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereIn('class_id', $teacherClassIds);
        }

        // Students have no access to class assignment management
        return $builder->where('id', 0);
    }
}
