<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramTaskInstance extends BaseModel
{
    use SoftDeletes;

    /**
     * Status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_MISSED = 'missed';
    const STATUS_EXCUSED = 'excused';

    /**
     * Assignment type constants.
     */
    const ASSIGNED_INDIVIDUAL = 'individual';
    const ASSIGNED_TEAM = 'team';

    /**
     * The table associated with the model.
     */
    protected $table = 'program_task_instances';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_task_id',
        'user_id',
        'start_date',
        'end_date',
        'status',
        'assigned_via',
        'team_id',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all status types.
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_MISSED => 'Missed',
            self::STATUS_EXCUSED => 'Excused',
        ];
    }

    /**
     * Get all assignment types.
     */
    public static function getAssignmentTypes(): array
    {
        return [
            self::ASSIGNED_INDIVIDUAL => 'Individual',
            self::ASSIGNED_TEAM => 'Team',
        ];
    }

    /**
     * Get status name.
     */
    public function getStatusNameAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? 'Unknown';
    }

    /**
     * Get assignment type name.
     */
    public function getAssignmentTypeNameAttribute(): string
    {
        return self::getAssignmentTypes()[$this->assigned_via] ?? 'Unknown';
    }

    /**
     * Get the program task this instance belongs to.
     */
    public function programTask(): BelongsTo
    {
        return $this->belongsTo(ProgramTask::class);
    }

    /**
     * Get the user (student) assigned to this task.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the team if assigned via team.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(ProgramTeam::class);
    }

    /**
     * Get the actions for this task instance.
     */
    public function actions(): HasMany
    {
        return $this->hasMany(ProgramTaskAction::class);
    }

    /**
     * Get the reading logs associated with this task instance.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(ProgramReadingLog::class);
    }

    /**
     * Scope to get pending tasks.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope to get missed tasks.
     */
    public function scopeMissed($query)
    {
        return $query->where('status', self::STATUS_MISSED);
    }

    /**
     * Scope to get overdue tasks.
     */
    public function scopeOverdue($query)
    {
        $now = now()->toDateString();
        return $query->where('status', self::STATUS_PENDING)
                    ->where('end_date', '<', $now);
    }

    /**
     * Scope to get active tasks (not completed/missed/excused).
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by team.
     */
    public function scopeForTeam($query, int $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->where('start_date', '>=', $startDate)
                    ->where('end_date', '<=', $endDate);
    }

    /**
     * Check if task is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->status === self::STATUS_PENDING && 
               $this->end_date < now()->toDateString();
    }

    /**
     * Check if task is completed.
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if task is active (can be completed).
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === self::STATUS_PENDING && 
               $this->start_date <= now()->toDateString() &&
               $this->end_date >= now()->toDateString();
    }

    /**
     * Get task duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get days remaining.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->status !== self::STATUS_PENDING) {
            return 0;
        }
        
        $now = now()->toDateString();
        if ($this->end_date < $now) {
            return 0;
        }
        
        return now()->diffInDays($this->end_date);
    }

    /**
     * Mark task as completed.
     */
    public function markCompleted(?int $completedBy = null, ?string $notes = null, ?int $points = null): ProgramTaskAction
    {
        $this->update(['status' => self::STATUS_COMPLETED]);
        
        $action = $this->actions()->create([
            'action_type' => ProgramTaskAction::TYPE_COMPLETED,
            'action_date' => now(),
            'notes' => $notes,
            'points_awarded' => $points ?? $this->programTask->points,
            'completed_by' => $completedBy,
        ]);

        // Award points if specified
        if ($action->points_awarded > 0) {
            ProgramUserPoint::create([
                'program_id' => $this->programTask->program_id,
                'user_id' => $this->user_id, 
                'point_source' => ProgramUserPoint::SOURCE_TASK,
                'points' => $action->points_awarded,
                'earned_at' => now(),
            ]);
        }

        return $action;
    }

    /**
     * Mark task as missed.
     */
    public function markMissed(?string $notes = null): ProgramTaskAction
    {
        $this->update(['status' => self::STATUS_MISSED]);
        
        return $this->actions()->create([
            'action_type' => ProgramTaskAction::TYPE_MISSED,
            'action_date' => now(),
            'notes' => $notes,
        ]);
    }

    /**
     * Mark task as excused.
     */
    public function markExcused(?string $notes = null, ?int $excusedBy = null): ProgramTaskAction
    {
        $this->update(['status' => self::STATUS_EXCUSED]);
        
        return $this->actions()->create([
            'action_type' => ProgramTaskAction::TYPE_EXCUSED,
            'action_date' => now(),
            'notes' => $notes,
            'completed_by' => $excusedBy,
        ]);
    }

    /**
     * Auto-mark overdue tasks as missed.
     */
    public static function markOverdueAsMissed(): int
    {
        $overdueInstances = self::overdue()->get();
        $markedCount = 0;
        
        foreach ($overdueInstances as $instance) {
            $instance->markMissed('Automatically marked as missed due to deadline');
            $markedCount++;
        }
        
        return $markedCount;
    }
}
