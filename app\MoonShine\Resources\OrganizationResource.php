<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;


use App\Models\Organization;
use App\Models\EnumOrganizationType;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;


#[Icon('building-office')]

class OrganizationResource extends BaseResource
{
    protected string $model = Organization::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.organizations');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),            
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                    
                    BelongsTo::make(__('admin.organization_type'), 'organizationType',
                        formatted: fn(EnumOrganizationType $type) => $type->name,
                        resource: EnumOrganizationTypeResource::class)
                        ->placeholder(__('admin.select_organization_type')),
                ]),
                
                Flex::make([
                    Switcher::make(__('admin.active'), 'active')
                        ->default(true),
                ]),
            ]),
            

            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
                Text::make(__('admin.name'), 'name'),
                BelongsTo::make(__('admin.organization_type'), 'organizationType',
                    formatted: fn(EnumOrganizationType $type) => $type->name,
                    resource: EnumOrganizationTypeResource::class),

                Switcher::make(__('admin.active'), 'active'),          
                
                HasMany::make(__('admin.school_classes'), 'schoolClasses', 
                    resource: SchoolClassResource::class),                
                ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'organization_type_id' => ['nullable', 'exists:enum_organization_types,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
