<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\GradeLevel;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;


#[Icon('building-office')]

class OrganizationResource extends BaseResource
{
    protected string $model = Organization::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.organizations');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Select::make(__('admin.type'), 'org_type')
                ->options(Organization::getTypes())
                ->sortable(),


            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),            
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                    
                    Select::make(__('admin.type'), 'org_type')
                        ->options(Organization::getTypes())
                        ->required()
                        ->placeholder(__('admin.select_type')),
                ]),
                
                Flex::make([
                    Switcher::make(__('admin.active'), 'active')
                        ->default(true),
                ]),
            ]),
            
            Box::make(__('admin.grade_levels'), [
                BelongsToMany::make(__('admin.grade_levels'), 'gradeLevels', 
                    formatted: fn(GradeLevel $grade) => $grade->formatted_name,
                    resource: GradeLevelResource::class),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
                Text::make(__('admin.name'), 'name'),
                    Select::make(__('admin.type'), 'org_type')
                        ->options(Organization::getTypes()),
                    
                    Switcher::make(__('admin.active'), 'active'),
                

                
                BelongsToMany::make(__('admin.grade_levels'), 'gradeLevels', 
                    formatted: fn(GradeLevel $grade) => $grade->formatted_name,
                    resource: GradeLevelResource::class)
                    ->inline(separator: ', '),
                
                HasMany::make(__('admin.school_classes'), 'schoolClasses', 
                    resource: SchoolClassResource::class),                
                ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'org_type' => ['required', 'integer', 'in:1,2'],

            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
