<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserClass extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'class_id',
        'organization_id',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the class.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the organization.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by class.
     */
    public function scopeByClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get the display name for this assignment.
     */
    public function getDisplayNameAttribute(): string
    {
        $parts = [
            $this->user->name,
            $this->schoolClass->full_name,
            $this->organization->name,
        ];

        return implode(' - ', array_filter($parts));
    }

    /**
     * Get assignment summary.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->user->name . ' in ' . $this->schoolClass->full_name;
        
        if ($this->organization) {
            $summary .= ' at ' . $this->organization->name;
        }
        
        return $summary;
    }

    /**
     * Activate this assignment.
     */
    public function activate(): bool
    {
        return $this->update(['active' => true]);
    }

    /**
     * Deactivate this assignment.
     */
    public function deactivate(): bool
    {
        return $this->update(['active' => false]);
    }

    /**
     * Check if this assignment is active.
     */
    public function isActive(): bool
    {
        return $this->active;
    }
}
