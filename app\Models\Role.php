<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Sweet1s\MoonshineRBAC\Traits\HasMoonShineRolePermissions;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    use HasMoonShineRolePermissions;

    // TODO: Restrict admin panel login by role: https://moonshine-laravel.com/en/docs/3.x/security/authentication#role-based-access


    protected $with = ['permissions'];    

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'created_by',
        'updated_by',
    ];}
