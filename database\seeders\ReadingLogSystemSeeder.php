<?php

namespace Database\Seeders;

use App\Models\Program;
use App\Models\Book;
use App\Models\User;
use App\Models\ProgramUserBook;
use App\Models\ProgramReadingLog;
use App\Models\ProgramTask;
use App\Models\ProgramTaskInstance;
use App\Services\ReadingLogService;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class ReadingLogSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding reading log system...');
        
        // Get test data
        $program = Program::where('is_active', true)->first();
        $book = Book::first();
        $students = User::whereHas('termUsers', function($query) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5); // Student level
            });
        })->limit(3)->get();
        
        if (!$program || !$book || $students->isEmpty()) {
            $this->command->warn('No active program, book, or students found for reading log seeding.');
            return;
        }
        
        // Ensure students have book assignments
        $this->ensureBookAssignments($program, $book, $students);
        
        // Create reading log tasks
        $this->createReadingLogTasks($program, $students);
        
        // Create sample reading logs
        $this->createSampleReadingLogs($program, $book, $students);
        
        // Display statistics
        $this->displayStatistics($program);
        
        $this->command->info('Reading log system seeding completed successfully!');
    }
    
    /**
     * Ensure students have book assignments.
     */
    private function ensureBookAssignments(Program $program, Book $book, $students): void
    {
        foreach ($students as $student) {
            $user = $student->users()->first();
            
            if (!$user) {
                continue;
            }
            
            $assignment = ProgramUserBook::where('program_id', $program->id)
                                        ->where('book_id', $book->id)
                                        ->where('user_id', $user->id)
                                        ->first();
            
            if (!$assignment) {
                ProgramUserBook::create([
                    'program_id' => $program->id,
                    'book_id' => $book->id,
                    'user_id' => $user->id,
                    'start_date' => now()->subDays(30),
                ]);
                
                $this->command->info("Created book assignment for student: {$student->name}");
            }
        }
    }
    
    /**
     * Create reading log tasks.
     */
    private function createReadingLogTasks(Program $program, $students): void
    {
        // Check if reading log task already exists
        $existingTask = ProgramTask::where('program_id', $program->id)
                                  ->where('task_type', ProgramTask::TYPE_READING_LOG)
                                  ->first();
        
        if ($existingTask) {
            $this->command->info('Reading log task already exists');
            return;
        }
        
        $readingLogService = new ReadingLogService();
        
        $userIds = $students->pluck('id')->toArray();
        $startDate = now()->subDays(14);
        $endDate = now()->addDays(7);
        
        $result = $readingLogService->generateReadingLogTasks(
            $program->id,
            $userIds,
            $startDate,
            $endDate,
            null, // All books
            15 // 15 points per log
        );
        
        $this->command->info("Created reading log task with {$result['instances_created']} instances");
        
        if (!empty($result['errors'])) {
            $this->command->warn('Some task instances failed to create:');
            foreach (array_slice($result['errors'], 0, 5) as $error) {
                $this->command->warn("  - {$error}");
            }
        }
    }
    
    /**
     * Create sample reading logs.
     */
    private function createSampleReadingLogs(Program $program, Book $book, $students): void
    {
        $readingLogService = new ReadingLogService();
        
        foreach ($students as $student) {
            // Create reading logs for the past 10 days
            for ($i = 10; $i >= 1; $i--) {
                $readingDate = now()->subDays($i);
                
                // Skip some days to create realistic patterns
                if ($i % 3 === 0) {
                    continue; // Skip every 3rd day
                }
                
                try {
                    // Generate realistic reading session data
                    $startPage = ($i - 1) * 8 + 1; // Progressive reading
                    $endPage = $startPage + rand(5, 15); // 5-15 pages per session
                    $duration = rand(15, 45); // 15-45 minutes
                    $notes = $this->generateReadingNotes($i);
                    
                    $log = $readingLogService->createReadingLog(
                        $program->id,
                        $book->id,
                        $student->id,
                        $readingDate,
                        $startPage,
                        $endPage,
                        $duration,
                        $notes
                    );
                    
                    // Randomly verify some logs
                    if (rand(1, 3) === 1) { // 33% chance of verification
                        $teacher = User::whereHas('termUsers', function($query) {
                            $query->whereHas('role', function($roleQuery) {
                                $roleQuery->where('level', 4); // Teacher level
                            });
                        })->first();
                        
                        if ($teacher) {
                            $readingLogService->verifyReadingLog($log, $teacher->id, 'Good progress!');
                        }
                    }
                    
                } catch (\Exception $e) {
                    // Skip if log already exists or other error
                    continue;
                }
            }
            
            $this->command->info("Created reading logs for student: {$student->name}");
        }
    }
    
    /**
     * Generate realistic reading notes.
     */
    private function generateReadingNotes(int $day): string
    {
        $notes = [
            'Really enjoyed this chapter! The characters are developing well.',
            'The plot is getting more interesting. Can\'t wait to see what happens next.',
            'This part was a bit confusing, but I think I understand the main idea.',
            'Great action scene! Very exciting to read.',
            'The author\'s description of the setting is very vivid.',
            'I can relate to the main character\'s feelings in this chapter.',
            'This chapter answered some of my questions from earlier.',
            'The dialogue feels very natural and realistic.',
            'I\'m starting to see how all the pieces fit together.',
            'This was a slower chapter but important for character development.',
        ];
        
        return $notes[($day - 1) % count($notes)];
    }
    
    /**
     * Display reading log statistics.
     */
    private function displayStatistics(Program $program): void
    {
        $readingLogService = new ReadingLogService();
        
        // Get dashboard data for the past 30 days
        $dashboard = $readingLogService->getReadingLogDashboard($program->id);
        
        $this->command->info('Reading Log Statistics:');
        $this->command->info("📚 Total Logs: {$dashboard['total_logs']}");
        $this->command->info("📖 Total Pages Read: {$dashboard['total_pages_read']}");
        $this->command->info("🏆 Total Points Awarded: {$dashboard['total_points_awarded']}");
        $this->command->info("✅ Verified Logs: {$dashboard['verified_logs']}");
        $this->command->info("⏳ Unverified Logs: {$dashboard['unverified_logs']}");
        $this->command->info("👥 Active Students: {$dashboard['unique_students']}");
        $this->command->info("📚 Books Being Read: {$dashboard['unique_books']}");
        $this->command->info("📊 Average Pages per Session: {$dashboard['average_pages_per_session']}");
        $this->command->info("⏱️ Average Duration: " . round($dashboard['average_duration_minutes'], 1) . " minutes");
        
        // Show top readers
        if (!empty($dashboard['top_readers'])) {
            $this->command->info('🏆 Top Readers:');
            foreach (array_slice($dashboard['top_readers'], 0, 3) as $index => $reader) {
                $rank = $index + 1;
                $this->command->info("  {$rank}. {$reader['user_name']}: {$reader['total_pages']} pages ({$reader['total_sessions']} sessions)");
            }
        }
        
        // Show reading streaks
        $students = User::whereHas('termUsers', function($query) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5);
            });
        })->limit(3)->get();
        
        $book = Book::first();
        
        if ($book) {
            $this->command->info('📈 Reading Streaks:');
            foreach ($students as $student) {
                $streak = $readingLogService->getReadingStreak($program->id, $book->id, $student->id);
                $this->command->info("  {$student->name}: {$streak['current_streak']} days (longest: {$streak['longest_streak']})");
            }
        }
        
        // Show recent activity
        $recentLogs = ProgramReadingLog::where('program_id', $program->id)
                                     ->with(['user', 'book'])
                                     ->orderBy('reading_date', 'desc')
                                     ->limit(5)
                                     ->get();
        
        if ($recentLogs->isNotEmpty()) {
            $this->command->info('📅 Recent Reading Activity:');
            foreach ($recentLogs as $log) {
                $verified = $log->is_verified ? '✅' : '⏳';
                $this->command->info("  {$verified} {$log->user->name}: {$log->pages_read} pages on {$log->reading_date->format('M j')}");
            }
        }
    }
}
