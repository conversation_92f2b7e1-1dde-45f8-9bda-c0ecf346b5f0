- ## Prologue
    - [Contribution Guide](/docs/{{version}}/contribution)
    - [Upgrade guide](/docs/{{version}}/upgrade-guide)
    - [Support Policy](/docs/{{version}}/support-policy)
    - [Troubleshooting](/docs/{{version}}/troubleshooting)
- ## Getting Started
    - [Quick Start](/docs/{{version}}/quick-start)
    - [Installation](/docs/{{version}}/installation)
    - [Configuration](/docs/{{version}}/configuration)
- ## Appearance
    - [Menu](/docs/{{version}}/appearance/menu)
    - [Layout](/docs/{{version}}/appearance/layout)
    - [Assets](/docs/{{version}}/appearance/assets)
    - [Colors](/docs/{{version}}/appearance/colors)
    - [Icons](/docs/{{version}}/appearance/icons)
- ## ModelResource
    - [Basics](/docs/{{version}}/model-resource/index)
    - [Fields](/docs/{{version}}/model-resource/fields)
    - [With pages](/docs/{{version}}/model-resource/pages)
    - [Table](/docs/{{version}}/model-resource/table)
    - [Form](/docs/{{version}}/model-resource/form)
    - [Buttons](/docs/{{version}}/model-resource/buttons)
    - [Filters](/docs/{{version}}/model-resource/filters)
    - [Search](/docs/{{version}}/model-resource/search)
    - [Query](/docs/{{version}}/model-resource/query)
    - [Tags](/docs/{{version}}/model-resource/query-tags)
    - [Metrics](/docs/{{version}}/model-resource/metrics)
    - [Events](/docs/{{version}}/model-resource/events)
    - [Import/Export](/docs/{{version}}/model-resource/import-export)
    - [Routes](/docs/{{version}}/model-resource/routes)
    - [Authorization](/docs/{{version}}/model-resource/authorization)
- ## Pages
    - [Basics](/docs/{{version}}/page/index)
- ## Fields
    - [Conception](/docs/{{version}}/fields/index)
    - [Basic methods](/docs/{{version}}/fields/basic-methods)
    - [Checkbox](/docs/{{version}}/fields/checkbox)
    - [Code](/docs/{{version}}/fields/code)
    - [Color](/docs/{{version}}/fields/color)
    - [Date](/docs/{{version}}/fields/date)
    - [DateRange](/docs/{{version}}/fields/date-range)
    - [Enum](/docs/{{version}}/fields/enum)
    - [E-mail](/docs/{{version}}/fields/email)
    - [Fieldset](/docs/{{version}}/fields/fieldset)
    - [File](/docs/{{version}}/fields/file)
    - [Hidden](/docs/{{version}}/fields/hidden)
    - [HiddenIds](/docs/{{version}}/fields/hidden-ids)
    - [ID](/docs/{{version}}/fields/id)
    - [Image](/docs/{{version}}/fields/image)
    - [Json](/docs/{{version}}/fields/json)
    - [Markdown](/docs/{{version}}/fields/markdown)
    - [Number](/docs/{{version}}/fields/number)
    - [Password](/docs/{{version}}/fields/password)
    - [Phone](/docs/{{version}}/fields/phone)
    - [Position](/docs/{{version}}/fields/position)
    - [Preview](/docs/{{version}}/fields/preview)
    - [Range](/docs/{{version}}/fields/range)
    - [RangeSlider](/docs/{{version}}/fields/range-slider)
    - [Select](/docs/{{version}}/fields/select)
    - [Slug](/docs/{{version}}/fields/slug)
    - [StackFields](/docs/{{version}}/fields/stack-fields)
    - [Switcher](/docs/{{version}}/fields/switcher)
    - [Template](/docs/{{version}}/fields/template)
    - [Text](/docs/{{version}}/fields/text)
    - [Textarea](/docs/{{version}}/fields/textarea)
    - [TinyMce](/docs/{{version}}/fields/tinymce)
    - [Url](/docs/{{version}}/fields/url)
    - [BelongsTo](/docs/{{version}}/fields/belongs-to)
    - [BelongsToMany](/docs/{{version}}/fields/belongs-to-many)
    - [HasMany](/docs/{{version}}/fields/has-many)
    - [HasManyThrough](/docs/{{version}}/fields/has-many-through)
    - [HasOne](/docs/{{version}}/fields/has-one)
    - [HasOneThrough](/docs/{{version}}/fields/has-one-through)
    - [MorphMany](/docs/{{version}}/fields/morph-many)
    - [MorphOne](/docs/{{version}}/fields/morph-one)
    - [MorphTo](/docs/{{version}}/fields/morph-to)
    - [MorphToMany](/docs/{{version}}/fields/morph-to-many)
    - [RelationRepeater](/docs/{{version}}/fields/relation-repeater)
- ## Components
    - [Basics](/docs/{{version}}/components/index)
    - [Attributes](/docs/{{version}}/components/attributes)
    - [ActionButton](/docs/{{version}}/components/action-button)
    - [FormBuilder](/docs/{{version}}/components/form-builder)
    - [TableBuilder](/docs/{{version}}/components/table-builder)
    - [ActionGroup](/docs/{{version}}/components/action-group)
    - [Alert](/docs/{{version}}/components/alert)
    - [Assets](/docs/{{version}}/components/assets)
    - [Badge](/docs/{{version}}/components/badge)
    - [Body](/docs/{{version}}/components/body)
    - [Boolean](/docs/{{version}}/components/boolean)
    - [Box](/docs/{{version}}/components/box)
    - [Breadcrumbs](/docs/{{version}}/components/breadcrumbs)
    - [Burger](/docs/{{version}}/components/burger)
    - [Card](/docs/{{version}}/components/card)
    - [CardsBuilder](/docs/{{version}}/components/cards-builder)
    - [Carousel](/docs/{{version}}/components/carousel)
    - [Collapse](/docs/{{version}}/components/collapse)
    - [Color](/docs/{{version}}/components/color)
    - [Components](/docs/{{version}}/components/components)
    - [Content](/docs/{{version}}/components/content)
    - [Divider](/docs/{{version}}/components/divider)
    - [Div](/docs/{{version}}/components/div)
    - [Dropdown](/docs/{{version}}/components/dropdown)
    - [Favicon](/docs/{{version}}/components/favicon)
    - [FieldsGroup](/docs/{{version}}/components/fields-group)
    - [Files](/docs/{{version}}/components/files)
    - [Flash](/docs/{{version}}/components/flash)
    - [FlexibleRender](/docs/{{version}}/components/flexible-render)
    - [Flex](/docs/{{version}}/components/flex)
    - [Footer](/docs/{{version}}/components/footer)
    - [Fragment](/docs/{{version}}/components/fragment)
    - [Grid](/docs/{{version}}/components/grid)
    - [Head](/docs/{{version}}/components/head)
    - [Header](/docs/{{version}}/components/header)
    - [Heading](/docs/{{version}}/components/heading)
    - [Html](/docs/{{version}}/components/html)
    - [Icon](/docs/{{version}}/components/icon)
    - [Layout](/docs/{{version}}/components/layout)
    - [LineBreak](/docs/{{version}}/components/line-break)
    - [Link](/docs/{{version}}/components/link)
    - [Loader](/docs/{{version}}/components/loader)
    - [Locales](/docs/{{version}}/components/locales)
    - [Logo](/docs/{{version}}/components/logo)
    - [Menu](/docs/{{version}}/components/menu)
    - [Meta](/docs/{{version}}/components/meta)
    - [Metrics](/docs/{{version}}/components/metrics)
    - [MobileBar](/docs/{{version}}/components/mobilebar)
    - [Modal](/docs/{{version}}/components/modal)
    - [Notifications](/docs/{{version}}/components/notifications)
    - [OffCanvas](/docs/{{version}}/components/off-canvas)
    - [Popover](/docs/{{version}}/components/popover)
    - [Profile](/docs/{{version}}/components/profile)
    - [Progressbar](/docs/{{version}}/components/progress-bar)
    - [Rating](/docs/{{version}}/components/rating)
    - [Sidebar](/docs/{{version}}/components/sidebar)
    - [Search](/docs/{{version}}/components/search)
    - [Spinner](/docs/{{version}}/components/spinner)
    - [Tabs](/docs/{{version}}/components/tabs)
    - [ThemeSwitcher](/docs/{{version}}/components/theme-switcher)
    - [Thumbnails](/docs/{{version}}/components/thumbnails)
    - [Title](/docs/{{version}}/components/title)
    - [TopBar](/docs/{{version}}/components/topbar)
    - [When](/docs/{{version}}/components/when)
    - [Wrapper](/docs/{{version}}/components/wrapper)
- ## Frontend
    - [Js](/docs/{{version}}/frontend/js)
    - [SDUI](/docs/{{version}}/frontend/sdui)
    - [API](/docs/{{version}}/frontend/api)
- ## Security
    - [Authorization](/docs/{{version}}/security/authorization)
    - [Authentication](/docs/{{version}}/security/authentication)
- ## Advanced
    - [CrudResource](/docs/{{version}}/advanced/crud-resource)
    - [Routes](/docs/{{version}}/advanced/routes)
    - [Commands](/docs/{{version}}/advanced/commands)
    - [Controllers](/docs/{{version}}/advanced/controllers)
    - [MoonShineJsonResponse](/docs/{{version}}/advanced/moonshine-json-response)
    - [Handlers](/docs/{{version}}/advanced/handlers)
    - [TypeCasts](/docs/{{version}}/advanced/type-casts)
    - [Notifications](/docs/{{version}}/advanced/notifications)
    - [Toasts](/docs/{{version}}/advanced/toasts)
    - [Localization](/docs/{{version}}/advanced/localization)
    - [Testing](/docs/{{version}}/advanced/testing)
    - [Package Development](/docs/{{version}}/advanced/package-development)
- ## Recipes
    - [Profile](/docs/{{version}}/recipes/profile)
    - [Select](/docs/{{version}}/recipes/select)
    - [Tabs](/docs/{{version}}/recipes/tabs)
    - [Dashboard settings](/docs/{{version}}/recipes/dashboard-settings)
    - [Mass edit](/docs/{{version}}/recipes/mass-edit)
    - [IndexPage with CardsBuilder](/docs/{{version}}/recipes/index-page-cards)
    - [Breadcrumbs](/docs/{{version}}/recipes/custom-breadcrumbs)
    - [Form events](/docs/{{version}}/recipes/form-with-events)
    - [HasMany with parent id](/docs/{{version}}/recipes/hasmany-parent-id)
    - [Async metrics](/docs/{{version}}/recipes/async-metrics)
    - [Change field logic](/docs/{{version}}/recipes/change-field-logic)
    - [Template](/docs/{{version}}/recipes/template)
    - [UpdateOnPreview pivot](/docs/{{version}}/recipes/update-on-preview-pivot)
    - [Menu authorization](/docs/{{version}}/recipes/menu-authorization)
    - [Async remove on click](/docs/{{version}}/recipes/async-remove-on-click)
    - [Fields group](/docs/{{version}}/recipes/fields-group)
    - [Multiple selectors/fragments](/docs/{{version}}/recipes/multiple-fragments-selectors)
    - [Change config](/docs/{{version}}/recipes/change-config)
    - [Paginator](/docs/{{version}}/recipes/table-paginator)
    - [Soft deletes](/docs/{{version}}/recipes/soft-deletes)
    - [Reorderable resource](/docs/{{version}}/recipes/reorderable-resource)
- ## FAQ
    - [FAQ](/docs/{{version}}/faq)
