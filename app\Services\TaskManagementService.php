<?php

namespace App\Services;

use App\Models\ProgramTask;
use App\Models\ProgramTaskInstance;
use App\Models\ProgramTeam;
use App\Models\ProgramUserPoint;
use App\Models\StoryRule;
use App\Models\ProgramUserLevel;
use App\Models\ProgramUserAchievement;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class TaskManagementService
{
    /**
     * Assign a task to individual students or teams.
     */
    public function assignTask(ProgramTask $task, array $userIds = [], ?int $teamId = null): int
    {
        $instancesCreated = 0;
        
        // If team is specified, get all team members
        if ($teamId) {
            $team = ProgramTeam::findOrFail($teamId);
            $teamMemberIds = $team->members()->pluck('user_id')->toArray();
            $userIds = array_merge($userIds, $teamMemberIds);
        }
        
        // Remove duplicates
        $userIds = array_unique($userIds);
        
        if ($task->is_recurring) {
            // Generate recurring instances
            $instancesCreated = $task->generateInstances($userIds, $teamId);
        } else {
            // Create single instances
            foreach ($userIds as $userId) {
                // Check if instance already exists
                $existingInstance = $task->instances()
                    ->where('user_id', $userId)
                    ->where('start_date', $task->start_date->toDateString())
                    ->first();
                
                if (!$existingInstance) {
                    ProgramTaskInstance::create([
                        'program_task_id' => $task->id,
                        'user_id' => $userId,
                        'start_date' => $task->start_date,
                        'end_date' => $task->end_date,
                        'assigned_via' => $teamId ? 'team' : 'individual',
                        'team_id' => $teamId,
                    ]);
                    $instancesCreated++;
                }
            }
        }
        
        return $instancesCreated;
    }

    /**
     * Complete a task instance and trigger story progression.
     */
    public function completeTask(ProgramTaskInstance $instance, ?int $completedBy = null, ?string $notes = null): bool
    {
        if ($instance->status !== ProgramTaskInstance::STATUS_PENDING) {
            return false;
        }
        
        // Mark task as completed
        $action = $instance->markCompleted($completedBy, $notes);
        
        // Check for story progression triggers
        $this->checkStoryProgression($instance);
        
        return true;
    }

    /**
     * Check and trigger story progression based on task completion.
     */
    protected function checkStoryProgression(ProgramTaskInstance $instance): void
    {
        $program = $instance->programTask->program;
        $userId = $instance->user_id;
        
        if (!$program->story) {
            return;
        }
        
        // Get current user level
        $currentLevel = ProgramUserLevel::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->first();
        
        // Check for next chapter unlock
        $this->checkChapterUnlock($program, $userId, $currentLevel);
        
        // Check for achievement unlock
        $this->checkAchievementUnlock($program, $userId);
    }

    /**
     * Check if user can unlock next chapter.
     */
    protected function checkChapterUnlock($program, $userId, $currentLevel): void
    {
        $story = $program->story;
        $nextChapter = null;
        
        if ($currentLevel) {
            // Find next chapter in sequence
            $nextChapter = $story->chapters()
                ->where('sequence', '>', $currentLevel->storyChapter->sequence)
                ->orderBy('sequence')
                ->first();
        } else {
            // Get first chapter
            $nextChapter = $story->chapters()
                ->orderBy('sequence')
                ->first();
        }
        
        if (!$nextChapter || !$nextChapter->unlock_rule_id) {
            return;
        }
        
        // Check if unlock rule is satisfied
        if ($this->checkUnlockRule($nextChapter->unlockRule, $program, $userId)) {
            // Update user level
            if ($currentLevel) {
                $currentLevel->update(['story_chapter_id' => $nextChapter->id]);
            } else {
                ProgramUserLevel::create([
                    'program_id' => $program->id,
                    'user_id' => $userId,
                    'story_chapter_id' => $nextChapter->id,
                ]);
            }
        }
    }

    /**
     * Check if user can unlock achievements.
     */
    protected function checkAchievementUnlock($program, $userId): void
    {
        $story = $program->story;
        
        // Get achievements not yet earned by user
        $earnedAchievementIds = ProgramUserAchievement::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->pluck('story_achievement_id')
            ->toArray();
        
        $availableAchievements = $story->achievements()
            ->whereNotIn('id', $earnedAchievementIds)
            ->whereNotNull('unlock_rule_id')
            ->get();
        
        foreach ($availableAchievements as $achievement) {
            if ($this->checkUnlockRule($achievement->unlockRule, $program, $userId)) {
                ProgramUserAchievement::create([
                    'program_id' => $program->id,
                    'user_id' => $userId,
                    'story_achievement_id' => $achievement->id,
                    'earned_at' => now(),
                ]);
            }
        }
    }

    /**
     * Check if a story rule is satisfied for a user.
     */
    protected function checkUnlockRule(StoryRule $rule, $program, $userId): bool
    {
        return match($rule->rule_type) {
            StoryRule::TYPE_POINTS => $this->checkPointsRule($rule, $program, $userId),
            StoryRule::TYPE_ACHIEVEMENT_COUNT => $this->checkAchievementCountRule($rule, $program, $userId),
            StoryRule::TYPE_BOOK_COUNT => $this->checkBookCountRule($rule, $program, $userId),
            StoryRule::TYPE_SPECIFIC_ACHIEVEMENTS => $this->checkSpecificAchievementsRule($rule, $program, $userId),
            StoryRule::TYPE_SPECIFIC_BOOKS => $this->checkSpecificBooksRule($rule, $program, $userId),
            StoryRule::TYPE_CHAPTER_COMPLETION => $this->checkChapterCompletionRule($rule, $program, $userId),
            default => false,
        };
    }

    /**
     * Check points rule.
     */
    protected function checkPointsRule(StoryRule $rule, $program, $userId): bool
    {
        $totalPoints = ProgramUserPoint::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->sum('points');
        
        return $totalPoints >= $rule->quantity;
    }

    /**
     * Check achievement count rule.
     */
    protected function checkAchievementCountRule(StoryRule $rule, $program, $userId): bool
    {
        $achievementCount = ProgramUserAchievement::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->count();
        
        return $achievementCount >= $rule->quantity;
    }

    /**
     * Check book count rule.
     */
    protected function checkBookCountRule(StoryRule $rule, $program, $userId): bool
    {
        // This would need to be implemented based on your book completion tracking
        // For now, return false as placeholder
        return false;
    }

    /**
     * Check specific achievements rule.
     */
    protected function checkSpecificAchievementsRule(StoryRule $rule, $program, $userId): bool
    {
        $requiredAchievementIds = $rule->details()
            ->where('required_type', 1) // Achievement type
            ->pluck('required_id')
            ->toArray();
        
        $earnedAchievementIds = ProgramUserAchievement::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->pluck('story_achievement_id')
            ->toArray();
        
        return count(array_intersect($requiredAchievementIds, $earnedAchievementIds)) >= $rule->quantity;
    }

    /**
     * Check specific books rule.
     */
    protected function checkSpecificBooksRule(StoryRule $rule, $program, $userId): bool
    {
        // This would need to be implemented based on your book completion tracking
        // For now, return false as placeholder
        return false;
    }

    /**
     * Check chapter completion rule.
     */
    protected function checkChapterCompletionRule(StoryRule $rule, $program, $userId): bool
    {
        $currentLevel = ProgramUserLevel::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->first();
        
        if (!$currentLevel) {
            return false;
        }
        
        return $currentLevel->storyChapter->sequence >= $rule->quantity;
    }

    /**
     * Auto-mark overdue tasks as missed.
     */
    public function processOverdueTasks(): int
    {
        return ProgramTaskInstance::markOverdueAsMissed();
    }

    /**
     * Get task statistics for a program.
     */
    public function getTaskStatistics(int $programId): array
    {
        $instances = ProgramTaskInstance::whereHas('programTask', function($query) use ($programId) {
            $query->where('program_id', $programId);
        });
        
        return [
            'total' => $instances->count(),
            'pending' => $instances->where('status', 'pending')->count(),
            'completed' => $instances->where('status', 'completed')->count(),
            'missed' => $instances->where('status', 'missed')->count(),
            'excused' => $instances->where('status', 'excused')->count(),
            'overdue' => $instances->where('status', 'pending')
                ->where('end_date', '<', now()->toDateString())
                ->count(),
        ];
    }
}
