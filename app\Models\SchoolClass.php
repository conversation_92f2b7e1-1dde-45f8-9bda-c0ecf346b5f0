<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SchoolClass extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'organization_id',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the organization this class belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }



    /**
     * Get user class assignments for this class.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class, 'class_id');
    }

    /**
     * Get active user class assignments for this class.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class, 'class_id')->where('active', true);
    }

    /**
     * Get users assigned to this class.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_classes', 'class_id', 'user_id')
                    ->withPivot(['organization_id', 'active', 'created_at', 'updated_at'])
                    ->withTimestamps();
    }

    /**
     * Get active users assigned to this class.
     */
    public function activeUsers()
    {
        return $this->belongsToMany(User::class, 'user_classes', 'class_id', 'user_id')
                    ->withPivot(['organization_id', 'active', 'created_at', 'updated_at'])
                    ->wherePivot('active', true)
                    ->withTimestamps();
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to filter by grade level.
     */
    public function scopeByGradeLevel($query, $gradeLevelId)
    {
        return $query->where('grade_level_id', $gradeLevelId);
    }

    
    /**
     * Get the display name for the class.
     */
/*    
    public function getDisplayNameAttribute(): string
    {
        return $this->full_name . ' (' . $this->organization->name . ')';
    }
*/
    /**
     * Get students in this class.
     */
    public function getStudentsAttribute()
    {
        return $this->activeUsers()
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'Student');
                    })
                    ->get();
    }

    /**
     * Get teachers assigned to this class.
     */
    public function getTeachersAttribute()
    {
        return $this->activeUsers()
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'Teacher');
                    })
                    ->get();
    }

    /**
     * Get student count for this class.
     */
    public function getStudentCountAttribute(): int
    {
        return $this->activeUsers()
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'Student');
                    })
                    ->count();
    }
}
