<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserClass;
use Spatie\Permission\Models\Role;

class TestRoleBasedMenu extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:role-menu';

    /**
     * The console command description.
     */
    protected $description = 'Test role-based menu functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Role-Based Menu System Test ===');
        $this->newLine();

        // Test 1: Check if roles exist
        $this->info('1. Checking available roles:');
        $roles = Role::orderBy('name')->get();
        foreach ($roles as $role) {
            $this->line("   - {$role->name}");
        }
        $this->newLine();

        // Test 2: Check users by role
        $this->info('2. Checking users by role:');
        foreach ($roles as $role) {
            $userCount = User::where('role_id', $role->id)->count();
            $this->line("   - {$role->name}: {$userCount} users");
        }
        $this->newLine();

        // Test 3: Test menu visibility logic
        $this->info('3. Testing menu visibility logic:');
        
        // Test System Admin
        $systemAdmin = User::whereHas('roles', function($q) {
            $q->where('name', 'System Administrator');
        })->first();
        
        if ($systemAdmin) {
            $this->line("   System Admin ({$systemAdmin->name}):");
            $this->line("     - Should see: System menu, School Classes, Users");
            $this->line("     - Can see System menu: " . ($systemAdmin->isSystemAdmin() ? 'YES' : 'NO'));
            $this->line("     - Can see School Classes: " . (($systemAdmin->isSystemAdmin() || $systemAdmin->isSchoolAdmin()) ? 'YES' : 'NO'));
            $this->line("     - Sees Users (not My Students): " . (($systemAdmin->isSystemAdmin() || $systemAdmin->isSchoolAdmin()) ? 'YES' : 'NO'));
        } else {
            $this->line("   - No System Admin found");
        }
        $this->newLine();

        // Test School Admin
        $schoolAdmin = User::whereHas('roles', function($q) {
            $q->where('name', 'Admin');
        })->first();
        
        if ($schoolAdmin) {
            $this->line("   School Admin ({$schoolAdmin->name}):");
            $this->line("     - Should see: School Classes, Users (NOT System menu)");
            $this->line("     - Can see System menu: " . ($schoolAdmin->isSystemAdmin() ? 'YES' : 'NO'));
            $this->line("     - Can see School Classes: " . (($schoolAdmin->isSystemAdmin() || $schoolAdmin->isSchoolAdmin()) ? 'YES' : 'NO'));
            $this->line("     - Sees Users (not My Students): " . (($schoolAdmin->isSystemAdmin() || $schoolAdmin->isSchoolAdmin()) ? 'YES' : 'NO'));
        } else {
            $this->line("   - No School Admin found");
        }
        $this->newLine();

        // Test Teacher
        $teacher = User::whereHas('roles', function($q) {
            $q->where('name', 'Teacher');
        })->first();
        
        if ($teacher) {
            $this->line("   Teacher ({$teacher->name}):");
            $this->line("     - Should see: My Students (NOT System menu or School Classes)");
            $this->line("     - Can see System menu: " . ($teacher->isSystemAdmin() ? 'YES' : 'NO'));
            $this->line("     - Can see School Classes: " . (($teacher->isSystemAdmin() || $teacher->isSchoolAdmin()) ? 'YES' : 'NO'));
            $this->line("     - Sees My Students: " . ($teacher->isTeacher() ? 'YES' : 'NO'));
            
            // Check teacher's class assignments
            $classAssignments = UserClass::where('user_id', $teacher->id)
                ->where('active', true)
                ->with('schoolClass')
                ->get();

            $this->line("     - Assigned to classes: " . $classAssignments->count());
            foreach ($classAssignments as $assignment) {
                if ($assignment->schoolClass) {
                    $this->line("       * {$assignment->schoolClass->full_name}");
                }
            }
        } else {
            $this->line("   - No Teacher found");
        }
        $this->newLine();

        // Test Student
        $student = User::whereHas('roles', function($q) {
            $q->where('name', 'Student');
        })->first();
        
        if ($student) {
            $this->line("   Student ({$student->name}):");
            $this->line("     - Should have: No admin panel access");
            $this->line("     - Is Student: " . ($student->isStudent() ? 'YES' : 'NO'));
        } else {
            $this->line("   - No Student found");
        }
        $this->newLine();

        // Test 4: Check TeacherStudentsResource filtering
        $this->info('4. Testing TeacherStudentsResource filtering:');
        if ($teacher) {
            // Get teacher's assigned class IDs
            $teacherClassIds = UserClass::where('user_id', $teacher->id)
                ->where('active', true)
                ->pluck('class_id')
                ->filter()
                ->toArray();

            $this->line("   Teacher's assigned class IDs: " . implode(', ', $teacherClassIds));

            if (!empty($teacherClassIds)) {
                // Count students in teacher's classes
                $studentsInClasses = User::whereHas('userClasses', function ($q) use ($teacherClassIds) {
                    $q->where('active', true)
                      ->whereIn('class_id', $teacherClassIds);
                })->whereHas('roles', function ($q) {
                    $q->where('name', 'Student');
                })->count();

                $this->line("   Students teacher can see: {$studentsInClasses}");
            } else {
                $this->line("   Teacher has no class assignments");
            }
        } else {
            $this->line("   Cannot test - no teacher found");
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        return 0;
    }
}
