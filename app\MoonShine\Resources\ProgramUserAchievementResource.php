<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserAchievement;
use App\Models\StoryAchievement;
use App\Models\User;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\StoryAchievementResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramUserAchievement>
 */
class ProgramUserAchievementResource extends BaseResource
{
    protected string $model = ProgramUserAchievement::class;

    protected array $with = ['program', 'storyAchievement', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_achievements');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name)
                ->sortable(),
            BelongsTo::make(__('admin.story_achievements'), 'storyAchievement', 
                formatted: fn(StoryAchievement $achievement) => $achievement->name)
                ->sortable(),
            Text::make(__('admin.achievement_type'), 'storyAchievement.type')
                ->badge(fn($type) => match($type) {
                    'item' => 'blue',
                    'badge' => 'green',
                    'reward' => 'yellow',
                    'trophy' => 'purple',
                    'collectible' => 'gray',
                    default => 'gray'
                }),
            Date::make(__('admin.earned_at'), 'earned_at')
                ->format('d.m.Y H:i')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.users'), 'user',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                BelongsTo::make(__('admin.story_achievements'), 'storyAchievement', 
                    formatted: fn(StoryAchievement $achievement) => $achievement->name,
                    resource: StoryAchievementResource::class)
                    ->required()
                    ->placeholder(__('admin.select_achievement')),
                
                Date::make(__('admin.earned_at'), 'earned_at')
                    ->required()
                    ->default(now()),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.users'), 'user',
                formatted: fn(User $user) => $user->name),
            BelongsTo::make(__('admin.story_achievements'), 'storyAchievement', 
                formatted: fn(StoryAchievement $achievement) => $achievement->name),
            Text::make(__('admin.achievement_type'), 'storyAchievement.type')
                ->badge(fn($type) => match($type) {
                    'item' => 'blue',
                    'badge' => 'green',
                    'reward' => 'yellow',
                    'trophy' => 'purple',
                    'collectible' => 'gray',
                    default => 'gray'
                }),
            Date::make(__('admin.earned_at'), 'earned_at')
                ->format('d.m.Y H:i'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.story_achievements'), 'storyAchievement', 
                formatted: fn(StoryAchievement $achievement) => $achievement->name,
                resource: StoryAchievementResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'user_id' => ['required', 'exists:users,id'],
            'story_achievement_id' => ['required', 'exists:story_achievements,id'],
            'earned_at' => ['required', 'date'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
