<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Add organization_type_id column after org_type
            $table->foreignId('organization_type_id')->nullable()->after('org_type')->constrained('enum_organization_types')->onDelete('set null');

            // Add index for better performance
            $table->index('organization_type_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['organization_type_id']);

            // Drop the index
            $table->dropIndex(['organization_type_id']);

            // Drop the column
            $table->dropColumn('organization_type_id');
        });
    }
};
