<?php

namespace App\Services;

use App\Models\BookQuestion;
use App\Models\BookWord;
use App\Models\BookActivityType;
use App\Models\ProgramBookQuiz;
use App\Models\ProgramBookQuizQuestion;
use App\Models\ProgramBookActivity;
use App\Models\ProgramUserBook;
use App\Models\Book;
use App\Models\Program;
use App\Models\User;
use Illuminate\Support\Collection;

class AssessmentService
{
    /**
     * Generate a quiz for a student after book completion.
     */
    public function generateCompletionQuiz(
        int $programId, 
        int $bookId, 
        int $userId, 
        int $questionCount = 10, 
        float $passingScore = 60.0,
        ?int $timeLimitMinutes = null
    ): ProgramBookQuiz {
        $attemptNumber = ProgramBookQuiz::getNextAttemptNumber($programId, $bookId, $userId, ProgramBookQuiz::TYPE_COMPLETION);
        
        $quiz = ProgramBookQuiz::create([
            'program_id' => $programId,
            'book_id' => $bookId,
            'user_id' => $userId,
            'quiz_type' => ProgramBookQuiz::TYPE_COMPLETION,
            'total_questions' => $questionCount,
            'passing_score' => $passingScore,
            'attempt_number' => $attemptNumber,
            'started_at' => now(),
            'time_limit_minutes' => $timeLimitMinutes,
        ]);
        
        $quiz->generateQuestions($questionCount);
        
        return $quiz;
    }

    /**
     * Generate a daily reading quiz for specific pages.
     */
    public function generateDailyReadingQuiz(
        int $programId,
        int $bookId,
        int $userId,
        int $pageStart,
        int $pageEnd,
        int $questionCount = 5,
        float $passingScore = 70.0
    ): ProgramBookQuiz {
        $attemptNumber = ProgramBookQuiz::getNextAttemptNumber($programId, $bookId, $userId, ProgramBookQuiz::TYPE_DAILY_READING);
        
        $quiz = ProgramBookQuiz::create([
            'program_id' => $programId,
            'book_id' => $bookId,
            'user_id' => $userId,
            'quiz_type' => ProgramBookQuiz::TYPE_DAILY_READING,
            'total_questions' => $questionCount,
            'passing_score' => $passingScore,
            'attempt_number' => $attemptNumber,
            'started_at' => now(),
            'time_limit_minutes' => 15, // Default 15 minutes for daily reading
        ]);
        
        $quiz->generateQuestions($questionCount, null, $pageStart, $pageEnd);
        
        return $quiz;
    }

    /**
     * Submit quiz answers and calculate results.
     */
    public function submitQuiz(ProgramBookQuiz $quiz, array $answers): array
    {
        if ($quiz->is_completed) {
            throw new \Exception('Quiz is already completed');
        }
        
        $results = [];
        
        foreach ($answers as $questionId => $answer) {
            $quizQuestion = $quiz->quizQuestions()->where('id', $questionId)->first();
            
            if ($quizQuestion && !$quizQuestion->is_answered) {
                $success = $quizQuestion->answerQuestion($answer);
                $results[$questionId] = [
                    'success' => $success,
                    'is_correct' => $quizQuestion->is_correct,
                    'points_earned' => $quizQuestion->points_earned,
                ];
            }
        }
        
        // Complete the quiz
        $quiz->complete();
        
        return [
            'quiz_results' => $quiz->results,
            'question_results' => $results,
            'is_passed' => $quiz->is_passed,
        ];
    }

    /**
     * Create a vocabulary activity for a student.
     */
    public function createVocabularyActivity(
        int $programId,
        int $bookId,
        int $userId,
        int $activityTypeId,
        ?int $taskInstanceId = null
    ): ProgramBookActivity {
        return ProgramBookActivity::create([
            'program_id' => $programId,
            'book_id' => $bookId,
            'user_id' => $userId,
            'activity_type_id' => $activityTypeId,
            'program_task_instance_id' => $taskInstanceId,
        ]);
    }

    /**
     * Complete an activity with content.
     */
    public function completeActivity(ProgramBookActivity $activity, string $content): bool
    {
        return $activity->complete($content);
    }

    /**
     * Review an activity by a teacher.
     */
    public function reviewActivity(
        ProgramBookActivity $activity, 
        int $reviewerId, 
        ?string $feedback = null, 
        ?int $bonusPoints = 0
    ): bool {
        return $activity->review($reviewerId, $feedback, $bonusPoints);
    }

    /**
     * Check if a student can take a completion quiz for a book.
     */
    public function canTakeCompletionQuiz(int $programId, int $bookId, int $userId): array
    {
        // Check if book is assigned to student
        $assignment = ProgramUserBook::where('program_id', $programId)
                                    ->where('book_id', $bookId)
                                    ->where('user_id', $userId)
                                    ->first();
        
        if (!$assignment) {
            return ['can_take' => false, 'reason' => 'Book not assigned to student'];
        }
        
        if (!$assignment->is_completed) {
            return ['can_take' => false, 'reason' => 'Book not completed yet'];
        }
        
        // Check if already passed
        $passedQuiz = ProgramBookQuiz::where('program_id', $programId)
                                   ->where('book_id', $bookId)
                                   ->where('user_id', $userId)
                                   ->where('quiz_type', ProgramBookQuiz::TYPE_COMPLETION)
                                   ->where('is_passed', true)
                                   ->first();
        
        if ($passedQuiz) {
            return ['can_take' => false, 'reason' => 'Already passed completion quiz'];
        }
        
        return ['can_take' => true, 'reason' => null];
    }

    /**
     * Get student's quiz history for a book.
     */
    public function getStudentQuizHistory(int $programId, int $bookId, int $userId): array
    {
        $quizzes = ProgramBookQuiz::where('program_id', $programId)
                                 ->where('book_id', $bookId)
                                 ->where('user_id', $userId)
                                 ->orderBy('attempt_number')
                                 ->get();
        
        return $quizzes->map(function($quiz) {
            return $quiz->results;
        })->toArray();
    }

    /**
     * Get student's activity history for a book.
     */
    public function getStudentActivityHistory(int $programId, int $bookId, int $userId): array
    {
        return ProgramBookActivity::getStudentActivities($programId, $userId, $bookId);
    }

    /**
     * Generate vocabulary questions from book words.
     */
    public function generateVocabularyQuestions(int $bookId, int $count = 10): array
    {
        $words = BookWord::getRandomWordsForActivity($bookId, $count);
        $questions = [];
        
        foreach ($words as $word) {
            $wordModel = BookWord::find($word['id']);
            $wordQuestions = $wordModel->generateVocabularyQuestions();
            $questions = array_merge($questions, $wordQuestions);
        }
        
        return array_slice($questions, 0, $count);
    }

    /**
     * Get assessment statistics for a program.
     */
    public function getAssessmentStatistics(int $programId, ?int $bookId = null): array
    {
        $quizQuery = ProgramBookQuiz::where('program_id', $programId);
        $activityQuery = ProgramBookActivity::where('program_id', $programId);
        
        if ($bookId) {
            $quizQuery = $quizQuery->where('book_id', $bookId);
            $activityQuery = $activityQuery->where('book_id', $bookId);
        }
        
        $quizzes = $quizQuery->get();
        $activities = $activityQuery->get();
        
        return [
            'quizzes' => [
                'total' => $quizzes->count(),
                'completed' => $quizzes->where('completed_at', '!=', null)->count(),
                'passed' => $quizzes->where('is_passed', true)->count(),
                'average_score' => $quizzes->where('completed_at', '!=', null)->avg('score_percentage') ?? 0,
                'by_type' => [
                    'completion' => $quizzes->where('quiz_type', ProgramBookQuiz::TYPE_COMPLETION)->count(),
                    'daily_reading' => $quizzes->where('quiz_type', ProgramBookQuiz::TYPE_DAILY_READING)->count(),
                    'practice' => $quizzes->where('quiz_type', ProgramBookQuiz::TYPE_PRACTICE)->count(),
                ],
            ],
            'activities' => ProgramBookActivity::getActivityStatistics($programId, $bookId),
        ];
    }

    /**
     * Get book readiness for assessment (questions and words available).
     */
    public function getBookAssessmentReadiness(int $bookId): array
    {
        $questions = BookQuestion::active()->forBook($bookId)->get();
        $words = BookWord::active()->forBook($bookId)->get();
        
        return [
            'questions' => [
                'total' => $questions->count(),
                'by_difficulty' => [
                    'easy' => $questions->where('difficulty_level', 'easy')->count(),
                    'medium' => $questions->where('difficulty_level', 'medium')->count(),
                    'hard' => $questions->where('difficulty_level', 'hard')->count(),
                ],
                'with_media' => $questions->filter(fn($q) => $q->hasMedia())->count(),
                'page_specific' => $questions->filter(fn($q) => $q->page_start && $q->page_end)->count(),
            ],
            'words' => BookWord::getWordsByDifficulty($bookId),
            'activity_types' => BookActivityType::active()->count(),
            'ready_for_assessment' => $questions->count() >= 10 && $words->count() >= 20,
        ];
    }

    /**
     * Bulk import questions for a book.
     */
    public function bulkImportQuestions(int $bookId, array $questionsData): array
    {
        $imported = 0;
        $errors = [];
        
        foreach ($questionsData as $index => $questionData) {
            try {
                $questionData['book_id'] = $bookId;
                BookQuestion::create($questionData);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row {$index}: " . $e->getMessage();
            }
        }
        
        return [
            'imported' => $imported,
            'errors' => $errors,
            'total' => count($questionsData),
        ];
    }

    /**
     * Bulk import vocabulary words for a book.
     */
    public function bulkImportWords(int $bookId, array $wordsData): array
    {
        $imported = 0;
        $errors = [];
        
        foreach ($wordsData as $index => $wordData) {
            try {
                $wordData['book_id'] = $bookId;
                BookWord::create($wordData);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row {$index}: " . $e->getMessage();
            }
        }
        
        return [
            'imported' => $imported,
            'errors' => $errors,
            'total' => count($wordsData),
        ];
    }
}
