<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class EnumOrganizationType extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'created_by',
        'updated_by',
    ];

    /**
     * Get organizations that have this type.
     */
    public function organizations(): HasMany
    {
        return $this->hasMany(Organization::class, 'organization_type_id');
    }
}
