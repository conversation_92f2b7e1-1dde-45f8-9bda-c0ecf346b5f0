<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Sweet1s\MoonshineRBAC\Traits\MoonshineRBACHasRoles;

class User extends Authenticatable
{
    use MoonshineRBACHasRoles;

    const SUPER_ADMIN_ROLE_ID = 1;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'name',
        'title',
        'email',
        'password',
        'role_id',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    public function isSystemAdmin(): bool
    {
        return $this->hasRole('system_admin');
    }

    /**
     * Check if user is school admin.
     */
    public function isSchoolAdmin(): bool
    {
        return $this->hasRole('school_admin');
    }

    /**
     * Check if user is teacher.
     */
    public function isTeacher(): bool
    {
        return $this->hasRole('teacher');
    }

    /**
     * Check if user is student.
     */
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }

    /**
     * Get the user who created this record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this record.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get organization assignments for this user.
     */
    public function organizationUsers(): HasMany
    {
        return $this->hasMany(OrganizationUser::class);
    }

    /**
     * Get active organization assignments for this user.
     */
    public function activeOrganizationUsers(): HasMany
    {
        return $this->hasMany(OrganizationUser::class)->where('active', true);
    }

    /**
     * Get class assignments for this user.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class);
    }

    /**
     * Get active class assignments for this user.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class)->where('active', true);
    }

    /**
     * Get organizations this user is assigned to.
     */
    public function organizations()
    {
        return $this->belongsToMany(Organization::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->withTimestamps();
    }

    /**
     * Get active organizations this user is assigned to.
     */
    public function activeOrganizations()
    {
        return $this->belongsToMany(Organization::class, 'organization_users')
                    ->withPivot(['role_id', 'active', 'created_at', 'updated_at'])
                    ->wherePivot('active', true)
                    ->withTimestamps();
    }

    /**
     * Get classes this user is assigned to.
     */
    public function classes()
    {
        return $this->belongsToMany(SchoolClass::class, 'user_classes', 'user_id', 'class_id')
                    ->withPivot(['organization_id', 'active', 'created_at', 'updated_at'])
                    ->withTimestamps();
    }

    /**
     * Get active classes this user is assigned to.
     */
    public function activeClasses()
    {
        return $this->belongsToMany(SchoolClass::class, 'user_classes', 'user_id', 'class_id')
                    ->withPivot(['organization_id', 'active', 'created_at', 'updated_at'])
                    ->wherePivot('active', true)
                    ->withTimestamps();
    }

    /**
     * Get reading logs for this user.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(ProgramReadingLog::class);
    }

    /**
     * Get the display name for the user.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get the user's agreements.
     */
    public function agreements(): HasMany
    {
        return $this->hasMany(UserAgreement::class);
    }

    /**
     * Check if user has accepted the current privacy policy.
     */
    public function hasAcceptedCurrentPrivacyPolicy(): bool
    {
        return UserAgreement::hasAcceptedCurrentPrivacyPolicy($this->id);
    }
}
