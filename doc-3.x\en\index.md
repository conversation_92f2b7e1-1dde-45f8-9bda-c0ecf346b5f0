# Introduction to MoonShine

- [What is MoonShine](#what-is-moonshine)
- [Features](#features)
- [Who is it for](#who-is-it-for)
- [Name history](#name-history)
- [Contributing](#contributing)

---

<a name="what-is-moonshine"></a>
## What is MoonShine

**MoonShine** is an open-source package for projects on **Laravel** (license **MIT**), designed for accelerated development of web projects. It is perfect for creating:

- Admin panels,
- `MVP` (minimum viable products),
- Backoffice applications,
- Content management systems (`CMS`).

**MoonShine** provides developers with tools for quickly creating functional and user-friendly administrative interfaces, working as a "framework on framework" and extending the capabilities of **Laravel**.

<a name="features"></a>
## Features

**MoonShine** has a number of key features:

- **Flexibility**: No strict binding to models; any data sources can be used,
- **Interface builder**: Convenient tools for creating forms and tables,
- **Modern technologies**: Use of `AlpineJs` for lightweight and simple frontend functionality,
- **Familiar tools**: `TailwindCSS` and `Blade`, familiar to most **Laravel** developers,
- **Extensibility**: Ability to use `Blade` and `Livewire` components,
- **Customization**: Convenient template builder, possibility to change colors and overall design.

<a name="who-is-it-for"></a>
## Who is it for

**MoonShine** is designed with the needs of developers of various skill levels in mind:

- **Beginners**: A low entry threshold allows for easy implementation of basic tasks such as authentication and `CRUD` operations.

> [!NOTE]
> To effectively use **MoonShine**, a basic understanding of **Laravel** is necessary. If you are a beginner, it is recommended to master the fundamentals of **Laravel** first.

- **Professionals**: The ability to use all the features of **Laravel** without restrictions while gaining tools for speeding up development.

<a name="name-history"></a>
## Name history

The name "**MoonShine**" has an interesting origin. It refers not so much to "moonlight" as to the process of making drinks independently in illegal conditions under the cover of night. It is a metaphor for the process of developing an admin panel: creating a quality product "with soul" in one's free time, mainly at night, initially for personal use and for friends.

<a name="contributing"></a>
## Contributing

**MoonShine** is an open-source project, and we welcome community contributions to its development. If you have ideas for improving **MoonShine** or its documentation:

1. Create an `Issue` on `GitHub` with a detailed description of your idea.
2. Ensure that your proposal contains enough details for understanding and implementation.

Your contribution is crucial for the project's development and improving the experience of using **MoonShine** for all developers.

![main](https://raw.githubusercontent.com/moonshine-software/doc/3.x/resources/screenshots/main.png#light)
![main_dark](https://raw.githubusercontent.com/moonshine-software/doc/3.x/resources/screenshots/main_dark.png#dark)
