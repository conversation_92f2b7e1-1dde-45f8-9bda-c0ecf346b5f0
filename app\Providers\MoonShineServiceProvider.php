<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use MoonShine\Contracts\Core\DependencyInjection\ConfiguratorContract;
use MoonShine\Contracts\Core\DependencyInjection\CoreContract;
use MoonShine\Laravel\DependencyInjection\MoonShine;
use MoonShine\Laravel\DependencyInjection\MoonShineConfigurator;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\RoleResource;
use App\MoonShine\Resources\OrganizationResource;
use App\MoonShine\Resources\GradeLevelResource;
use App\MoonShine\Resources\SchoolClassResource;
use App\MoonShine\Resources\OrganizationUserResource;
use App\MoonShine\Resources\UserClassResource;
use App\MoonShine\Resources\AuthorResource;
use App\MoonShine\Resources\PublisherResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\StoryResource;
use App\MoonShine\Resources\StoryRuleResource;
use App\MoonShine\Resources\StoryRuleDetailResource;
use App\MoonShine\Resources\StoryChapterResource;
use App\MoonShine\Resources\StoryCharacterResource;
use App\MoonShine\Resources\StoryCharacterStageResource;
use App\MoonShine\Resources\StoryAchievementResource;
use App\MoonShine\Resources\StoryBookResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\ProgramSchoolResource;
use App\MoonShine\Resources\ProgramClassResource;
use App\MoonShine\Resources\ProgramBookResource;
use App\MoonShine\Resources\ProgramTeamResource;
use App\MoonShine\Resources\ProgramTeamMemberResource;
use App\MoonShine\Resources\ProgramUserLevelResource;
use App\MoonShine\Resources\ProgramUserAchievementResource;
use App\MoonShine\Resources\ProgramUserCharacterResource;
use App\MoonShine\Resources\ProgramUserMapResource;
use App\MoonShine\Resources\ProgramUserPointResource;
use App\MoonShine\Resources\ProgramUserBookResource;
use App\MoonShine\Resources\ProgramTaskResource;
use App\MoonShine\Resources\ProgramTaskInstanceResource;
use App\MoonShine\Resources\ProgramTaskActionResource;
use App\MoonShine\Resources\BookQuestionResource;
use App\MoonShine\Resources\BookWordResource;
use App\MoonShine\Resources\BookActivityTypeResource;
use App\MoonShine\Resources\ProgramBookQuizResource;
use App\MoonShine\Resources\ProgramBookActivityResource;
use App\MoonShine\Resources\ProgramReadingLogResource;
use App\MoonShine\Resources\UserAgreementResource;
use Sweet1s\MoonshineRBAC\Resource\PermissionResource;
use App\MoonShine\Resources\EnumOrganizationTypeResource;


class MoonShineServiceProvider extends ServiceProvider
{
    /**
     * @param  MoonShine  $core
     * @param  MoonShineConfigurator  $config
     *
     */
    public function boot(CoreContract $core, ConfiguratorContract $config): void
    {
        // $config->authEnable();

        $core
            ->resources([
                // System Resources
                RoleResource::class,
                UserResource::class,
                PermissionResource::class,
                UserAgreementResource::class,

                // Academic Resources
                OrganizationResource::class,
                GradeLevelResource::class,
                SchoolClassResource::class,
                OrganizationUserResource::class,
                UserClassResource::class,

                // Book Resources
                AuthorResource::class,
                PublisherResource::class,
                BookResource::class,

                // Gamification Resources
                StoryResource::class,
                StoryRuleResource::class,
                StoryRuleDetailResource::class,
                StoryChapterResource::class,
                StoryCharacterResource::class,
                StoryCharacterStageResource::class,
                StoryAchievementResource::class,
                StoryBookResource::class,

                // Reading Program Resources
                ProgramResource::class,
                ProgramSchoolResource::class,
                ProgramClassResource::class,
                ProgramBookResource::class,
                ProgramTeamResource::class,
                ProgramTeamMemberResource::class,
                ProgramUserLevelResource::class,
                ProgramUserAchievementResource::class,
                ProgramUserCharacterResource::class,
                ProgramUserMapResource::class,
                ProgramUserPointResource::class,
                ProgramUserBookResource::class,

                // Task Management Resources
                ProgramTaskResource::class,
                ProgramTaskInstanceResource::class,
                ProgramTaskActionResource::class,

                // Assessment System Resources
                BookQuestionResource::class,
                BookWordResource::class,
                BookActivityTypeResource::class,
                ProgramBookQuizResource::class,
                ProgramBookActivityResource::class,

                // Reading Log System Resources
                ProgramReadingLogResource::class,
                EnumOrganizationTypeResource::class,
            ])
            ->pages([
                ...$config->getPages(),
            ])
        ;
    }
}
