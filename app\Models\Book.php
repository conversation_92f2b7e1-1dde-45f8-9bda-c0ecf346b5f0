<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Book extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'isbn',
        'publisher_id',
        'page_count',
        'year_of_publish',
        'cover_image',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'page_count' => 'integer',
            'year_of_publish' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the publisher of this book.
     */
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    /**
     * Get authors of this book.
     */
    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(Author::class, 'book_authors');
    }

    /**
     * Get tag values for this book.
     */
    public function tagValues(): HasMany
    {
        return $this->hasMany(TagValue::class, 'taggable_id')
                    ->where('taggable_type', TagValue::TYPE_BOOKS);
    }

    /**
     * Get tags for this book.
     */
    public function tags()
    {
        return Tag::whereIn('id', 
            $this->tagValues()->pluck('tag_id')
        )->get();
    }

    /**
     * Scope to search books by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('isbn', 'like', '%' . $search . '%');
    }

    /**
     * Scope to filter by publisher.
     */
    public function scopeByPublisher($query, $publisherId)
    {
        return $query->where('publisher_id', $publisherId);
    }

    /**
     * Scope to filter by year.
     */
    public function scopeByYear($query, int $year)
    {
        return $query->where('year_of_publish', $year);
    }

    /**
     * Scope to filter by year range.
     */
    public function scopeByYearRange($query, int $startYear, int $endYear)
    {
        return $query->whereBetween('year_of_publish', [$startYear, $endYear]);
    }

    /**
     * Scope to filter by page count range.
     */
    public function scopeByPageRange($query, int $minPages, int $maxPages)
    {
        return $query->whereBetween('page_count', [$minPages, $maxPages]);
    }

    /**
     * Scope to filter by author.
     */
    public function scopeByAuthor($query, $authorId)
    {
        return $query->whereHas('authors', function ($q) use ($authorId) {
            $q->where('authors.id', $authorId);
        });
    }

    /**
     * Scope to get recent books.
     */
    public function scopeRecent($query, int $years = 5)
    {
        $startYear = now()->year - $years;
        return $query->where('year_of_publish', '>=', $startYear);
    }

    /**
     * Get the primary author (first author).
     */
    public function getPrimaryAuthorAttribute(): ?Author
    {
        return $this->authors()->first();
    }

    /**
     * Get all author names as a string.
     */
    public function getAuthorNamesAttribute(): string
    {
        return $this->authors->pluck('name')->join(', ');
    }

    /**
     * Get formatted publication info.
     */
    public function getPublicationInfoAttribute(): string
    {
        return $this->publisher->name . ', ' . $this->year_of_publish;
    }

    /**
     * Get book age in years.
     */
    public function getAgeAttribute(): int
    {
        return now()->year - $this->year_of_publish;
    }

    /**
     * Check if book is recent (published in last 5 years).
     */
    public function isRecent(): bool
    {
        return $this->age <= 5;
    }

    /**
     * Get reading difficulty based on page count.
     */
    public function getDifficultyAttribute(): string
    {
        if ($this->page_count <= 50) {
            return 'Easy';
        } elseif ($this->page_count <= 150) {
            return 'Medium';
        } elseif ($this->page_count <= 300) {
            return 'Hard';
        } else {
            return 'Very Hard';
        }
    }

    /**
     * Get estimated reading time in hours.
     */
    public function getEstimatedReadingTimeAttribute(): float
    {
        // Assuming average reading speed of 250 words per minute
        // and approximately 250 words per page
        $wordsPerPage = 250;
        $wordsPerMinute = 250;
        
        $totalWords = $this->page_count * $wordsPerPage;
        $minutes = $totalWords / $wordsPerMinute;
        
        return round($minutes / 60, 1); // Convert to hours
    }

    /**
     * Add a tag to this book.
     */
    public function addTag(Tag $tag): ?TagValue
    {
        if ($tag->tag_type === Tag::TYPE_BOOKS) {
            return TagValue::createForModel($this, $tag);
        }
        
        return null;
    }

    /**
     * Remove a tag from this book.
     */
    public function removeTag(Tag $tag): bool
    {
        return $this->tagValues()
                    ->where('tag_id', $tag->id)
                    ->delete() > 0;
    }

    /**
     * Check if book has a specific tag.
     */
    public function hasTag(Tag $tag): bool
    {
        return $this->tagValues()
                    ->where('tag_id', $tag->id)
                    ->exists();
    }

    /**
     * Get the story books (stories this book is associated with).
     */
    public function storyBooks(): HasMany
    {
        return $this->hasMany(StoryBook::class);
    }

    /**
     * Get the stories associated with this book through story_books.
     */
    public function stories(): BelongsToMany
    {
        return $this->belongsToMany(Story::class, 'story_books')
                    ->withPivot(['sequence', 'created_by', 'updated_by', 'deleted_by', 'deleted_at'])
                    ->withTimestamps()
                    ->orderBy('sequence');
    }

    /**
     * Get the program user book assignments for this book.
     */
    public function programUserBooks(): HasMany
    {
        return $this->hasMany(ProgramUserBook::class);
    }

    /**
     * Get the questions for this book.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(BookQuestion::class);
    }

    /**
     * Get the active questions for this book.
     */
    public function activeQuestions(): HasMany
    {
        return $this->hasMany(BookQuestion::class)->where('is_active', true);
    }

    /**
     * Get the vocabulary words for this book.
     */
    public function words(): HasMany
    {
        return $this->hasMany(BookWord::class);
    }

    /**
     * Get the active vocabulary words for this book.
     */
    public function activeWords(): HasMany
    {
        return $this->hasMany(BookWord::class)->where('is_active', true);
    }

    /**
     * Get the quizzes for this book.
     */
    public function quizzes(): HasMany
    {
        return $this->hasMany(ProgramBookQuiz::class);
    }

    /**
     * Get the activities for this book.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(ProgramBookActivity::class);
    }

    /**
     * Get the reading logs for this book.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(ProgramReadingLog::class);
    }
}
