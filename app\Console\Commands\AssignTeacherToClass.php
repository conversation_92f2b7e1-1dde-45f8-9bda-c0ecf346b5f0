<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\OrganizationUser;
use App\Models\UserClass;
use App\Models\Organization;
use App\Models\SchoolClass;

class AssignTeacherToClass extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'assign:teacher-class';

    /**
     * The console command description.
     */
    protected $description = 'Assign a teacher to a class for testing role-based menu';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Assigning Teacher to Class ===');

        // Get or create organization
        $organization = Organization::first();
        if (!$organization) {
            $organization = Organization::create([
                'name' => 'Test School',
                'type' => 'school',
                'active' => true,
            ]);
            $this->info("Created organization: {$organization->name}");
        } else {
            $this->info("Using existing organization: {$organization->name}");
        }

        // Get or create school class
        $schoolClass = SchoolClass::first();
        if (!$schoolClass) {
            $schoolClass = SchoolClass::create([
                'name' => 'Test Class A',
                'organization_id' => $organization->id,
            ]);
            $this->info("Created school class: {$schoolClass->name}");
        } else {
            $this->info("Using existing school class: {$schoolClass->full_name}");
        }

        // Get teacher and student roles
        $teacherRole = Role::where('name', 'Teacher')->first();
        $studentRole = Role::where('name', 'Student')->first();

        if (!$teacherRole || !$studentRole) {
            $this->error('Teacher or Student role not found!');
            return 1;
        }

        // Get a teacher
        $teacher = User::whereHas('roles', function($q) {
            $q->where('name', 'Teacher');
        })->first();

        if (!$teacher) {
            $this->error('No teacher found!');
            return 1;
        }

        // First, assign teacher to organization
        $existingOrgAssignment = OrganizationUser::where('user_id', $teacher->id)
            ->where('organization_id', $organization->id)
            ->first();

        if (!$existingOrgAssignment) {
            OrganizationUser::create([
                'user_id' => $teacher->id,
                'organization_id' => $organization->id,
                'active' => true,
            ]);
            $this->info("Assigned teacher {$teacher->name} to organization {$organization->name}");
        } else {
            $this->info("Teacher {$teacher->name} is already assigned to organization {$organization->name}");
        }

        // Then, assign teacher to class
        $existingClassAssignment = UserClass::where('user_id', $teacher->id)
            ->where('class_id', $schoolClass->id)
            ->first();

        if (!$existingClassAssignment) {
            UserClass::create([
                'user_id' => $teacher->id,
                'organization_id' => $organization->id,
                'class_id' => $schoolClass->id,
                'active' => true,
            ]);
            $this->info("Assigned teacher {$teacher->name} to class {$schoolClass->full_name}");
        } else {
            $this->info("Teacher {$teacher->name} is already assigned to class {$schoolClass->full_name}");
        }

        // Get some students and assign them to the same class
        $students = User::whereHas('roles', function($q) {
            $q->where('name', 'Student');
        })->limit(3)->get();

        foreach ($students as $student) {
            // Assign student to organization
            $existingStudentOrgAssignment = OrganizationUser::where('user_id', $student->id)
                ->where('organization_id', $organization->id)
                ->first();

            if (!$existingStudentOrgAssignment) {
                OrganizationUser::create([
                    'user_id' => $student->id,
                    'organization_id' => $organization->id,
                    'active' => true,
                ]);
                $this->info("Assigned student {$student->name} to organization {$organization->name}");
            }

            // Assign student to class
            $existingStudentClassAssignment = UserClass::where('user_id', $student->id)
                ->where('class_id', $schoolClass->id)
                ->first();

            if (!$existingStudentClassAssignment) {
                UserClass::create([
                    'user_id' => $student->id,
                    'organization_id' => $organization->id,
                    'class_id' => $schoolClass->id,
                    'active' => true,
                ]);
                $this->info("Assigned student {$student->name} to class {$schoolClass->full_name}");
            } else {
                $this->info("Student {$student->name} is already assigned to class {$schoolClass->full_name}");
            }
        }

        $this->newLine();
        $this->info('=== Assignment Complete ===');
        $this->info('You can now test the TeacherStudentsResource filtering.');
        
        return 0;
    }
}
