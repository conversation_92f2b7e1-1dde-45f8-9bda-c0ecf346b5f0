<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\BookActivityType;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<BookActivityType>
 */
#[Icon('academic-cap')]
class BookActivityTypeResource extends BaseResource
{
    protected string $model = BookActivityType::class;

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.book_activity_types');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Select::make(__('admin.category'), 'category')
                ->options(BookActivityType::getCategories())
                ->sortable(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.description'), 'description'),
            
            Text::make(__('admin.word_count_range'), 'word_count_range')
                ->badge('blue'),
            
            Number::make(__('admin.points_base'), 'points_base')
                ->badge('green'),
            
            Switcher::make(__('admin.is_active'), 'is_active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Select::make(__('admin.category'), 'category')
                        ->options(BookActivityType::getCategories())
                        ->required()
                        ->placeholder(__('admin.select_category')),
                    
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_activity_name')),
                ]),
                
                Textarea::make(__('admin.description'), 'description')
                    ->placeholder(__('admin.enter_activity_description')),
                
                Flex::make([
                    Number::make(__('admin.min_word_count'), 'min_word_count')
                        ->min(0)
                        ->placeholder(__('admin.enter_min_word_count')),
                    
                    Number::make(__('admin.max_word_count'), 'max_word_count')
                        ->min(0)
                        ->placeholder(__('admin.enter_max_word_count')),
                ]),
                
                Flex::make([
                    Number::make(__('admin.points_base'), 'points_base')
                        ->required()
                        ->min(0)
                        ->default(10)
                        ->placeholder(__('admin.enter_base_points')),
                    
                    Switcher::make(__('admin.is_active'), 'is_active')
                        ->default(true),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.category'), 'category_name')
                ->badge('purple'),
            Text::make(__('admin.name'), 'name'),
            Textarea::make(__('admin.description'), 'description'),
            Number::make(__('admin.min_word_count'), 'min_word_count')
                ->badge('blue'),
            Number::make(__('admin.max_word_count'), 'max_word_count')
                ->badge('blue'),
            Text::make(__('admin.word_count_range'), 'word_count_range')
                ->badge('blue'),
            Number::make(__('admin.points_base'), 'points_base')
                ->badge('green'),
            Text::make(__('admin.instructions'), 'instructions'),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            Select::make(__('admin.category'), 'category')
                ->options(BookActivityType::getCategories()),
            Switcher::make(__('admin.is_active'), 'is_active'),
        ];
    }

    public function rules(mixed $item): array
    {
        $category = $item->category ?? request('category');
        
        return [
            'category' => ['required', 'in:vocabulary,spelling,writing,comprehension,creative'],
            'name' => [
                'required', 
                'string', 
                'max:255',
                "unique:book_activity_types,name,{$item->id},id,category,{$category}"
            ],
            'description' => ['nullable', 'string'],
            'min_word_count' => ['nullable', 'integer', 'min:0'],
            'max_word_count' => ['nullable', 'integer', 'min:0', 'gte:min_word_count'],
            'points_base' => ['required', 'integer', 'min:0'],
            'is_active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
