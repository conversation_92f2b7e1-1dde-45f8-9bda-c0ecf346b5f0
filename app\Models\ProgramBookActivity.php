<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramBookActivity extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_book_activities';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'book_id',
        'user_id',
        'activity_type_id',
        'program_task_instance_id',
        'activity_content',
        'word_count',
        'points_earned',
        'is_completed',
        'completed_at',
        'reviewed_by',
        'reviewed_at',
        'feedback',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_completed' => 'boolean',
            'word_count' => 'integer',
            'points_earned' => 'integer',
            'completed_at' => 'datetime',
            'reviewed_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program this activity belongs to.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the book this activity is about.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user (student) doing the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the activity type.
     */
    public function activityType(): BelongsTo
    {
        return $this->belongsTo(BookActivityType::class);
    }

    /**
     * Get the task instance if this activity is assigned as a task.
     */
    public function taskInstance(): BelongsTo
    {
        return $this->belongsTo(ProgramTaskInstance::class, 'program_task_instance_id');
    }

    /**
     * Get the reviewer (teacher).
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to get completed activities.
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope to get pending activities.
     */
    public function scopePending($query)
    {
        return $query->where('is_completed', false);
    }

    /**
     * Scope to get reviewed activities.
     */
    public function scopeReviewed($query)
    {
        return $query->whereNotNull('reviewed_by');
    }

    /**
     * Scope to get unreviewed activities.
     */
    public function scopeUnreviewed($query)
    {
        return $query->whereNull('reviewed_by');
    }

    /**
     * Scope to filter by activity type.
     */
    public function scopeOfType($query, int $activityTypeId)
    {
        return $query->where('activity_type_id', $activityTypeId);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->whereHas('activityType', function($q) use ($category) {
            $q->where('category', $category);
        });
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Check if activity is reviewed.
     */
    public function getIsReviewedAttribute(): bool
    {
        return !is_null($this->reviewed_by);
    }

    /**
     * Check if activity is assigned as a task.
     */
    public function getIsTaskAssignedAttribute(): bool
    {
        return !is_null($this->program_task_instance_id);
    }

    /**
     * Calculate word count from content.
     */
    public function calculateWordCount(): int
    {
        if (empty($this->activity_content)) {
            return 0;
        }
        
        $wordCount = str_word_count(strip_tags($this->activity_content));
        $this->word_count = $wordCount;
        
        return $wordCount;
    }

    /**
     * Complete the activity.
     */
    public function complete(?string $content = null): bool
    {
        if ($this->is_completed) {
            return false; // Already completed
        }
        
        if ($content) {
            $this->activity_content = $content;
            $this->calculateWordCount();
        }
        
        // Validate word count requirements
        if ($this->activityType->hasWordCountRequirements()) {
            $validation = $this->activityType->validateWordCount($this->word_count);
            if (!$validation['valid']) {
                return false; // Word count requirements not met
            }
        }
        
        $this->is_completed = true;
        $this->completed_at = now();
        
        // Calculate and award points
        $this->calculatePoints();
        
        $saved = $this->save();
        
        if ($saved) {
            $this->awardPoints();
            
            // Mark related task as completed if applicable
            if ($this->is_task_assigned && $this->taskInstance) {
                $this->taskInstance->markCompleted(
                    $this->user_id,
                    "Activity completed: {$this->activityType->name}",
                    $this->points_earned
                );
            }
        }
        
        return $saved;
    }

    /**
     * Calculate points for the activity.
     */
    protected function calculatePoints(): void
    {
        $qualityMultiplier = 1.0;
        
        // Adjust multiplier based on word count quality
        if ($this->activityType->isWritingBased() && $this->word_count > 0) {
            $minWords = $this->activityType->min_word_count ?? 50;
            if ($this->word_count >= $minWords * 1.5) {
                $qualityMultiplier = 1.3; // 30% bonus for substantial content
            } elseif ($this->word_count >= $minWords * 1.2) {
                $qualityMultiplier = 1.1; // 10% bonus for good content
            }
        }
        
        $this->points_earned = $this->activityType->calculatePoints($this->word_count, $qualityMultiplier);
    }

    /**
     * Award points to the student.
     */
    protected function awardPoints(): void
    {
        if ($this->points_earned > 0) {
            ProgramUserPoint::create([
                'program_id' => $this->program_id,
                'user_id' => $this->user_id,
                'point_source' => ProgramUserPoint::SOURCE_ACTIVITY,
                'points' => $this->points_earned,
                'earned_at' => now(),
            ]);
        }
    }

    /**
     * Review the activity.
     */
    public function review(int $reviewerId, ?string $feedback = null, ?int $bonusPoints = 0): bool
    {
        $this->reviewed_by = $reviewerId;
        $this->reviewed_at = now();
        $this->feedback = $feedback;
        
        // Award bonus points if provided
        if ($bonusPoints > 0) {
            $this->points_earned += $bonusPoints;
            
            // Create additional point record for bonus
            ProgramUserPoint::create([
                'program_id' => $this->program_id,
                'user_id' => $this->user_id,
                'point_source' => ProgramUserPoint::SOURCE_BONUS,
                'points' => $bonusPoints,
                'earned_at' => now(),
            ]);
        }
        
        return $this->save();
    }

    /**
     * Get activity summary.
     */
    public function getActivitySummaryAttribute(): array
    {
        return [
            'id' => $this->id,
            'activity_type' => $this->activityType->name,
            'category' => $this->activityType->category_name,
            'book_name' => $this->book->name,
            'student_name' => $this->user->name,
            'word_count' => $this->word_count,
            'points_earned' => $this->points_earned,
            'is_completed' => $this->is_completed,
            'is_reviewed' => $this->is_reviewed,
            'completed_at' => $this->completed_at?->format('Y-m-d H:i:s'),
            'reviewed_at' => $this->reviewed_at?->format('Y-m-d H:i:s'),
            'is_task_assigned' => $this->is_task_assigned,
        ];
    }

    /**
     * Get activity details for display.
     */
    public function getActivityDetailsAttribute(): array
    {
        return [
            'activity_summary' => $this->activity_summary,
            'activity_content' => $this->activity_content,
            'feedback' => $this->feedback,
            'word_count_validation' => $this->activityType->hasWordCountRequirements() ? 
                $this->activityType->validateWordCount($this->word_count) : null,
            'activity_instructions' => $this->activityType->instructions,
        ];
    }

    /**
     * Get activities for a student in a program.
     */
    public static function getStudentActivities(int $programId, int $userId, ?int $bookId = null): array
    {
        $query = static::with(['activityType', 'book', 'reviewer'])
                      ->where('program_id', $programId)
                      ->where('user_id', $userId);
        
        if ($bookId) {
            $query = $query->where('book_id', $bookId);
        }
        
        return $query->orderBy('created_at', 'desc')
                    ->get()
                    ->map(function($activity) {
                        return $activity->activity_summary;
                    })
                    ->toArray();
    }

    /**
     * Get activity statistics for a program.
     */
    public static function getActivityStatistics(int $programId, ?int $bookId = null): array
    {
        $query = static::where('program_id', $programId);
        
        if ($bookId) {
            $query = $query->where('book_id', $bookId);
        }
        
        $activities = $query->get();
        
        return [
            'total_activities' => $activities->count(),
            'completed_activities' => $activities->where('is_completed', true)->count(),
            'pending_activities' => $activities->where('is_completed', false)->count(),
            'reviewed_activities' => $activities->whereNotNull('reviewed_by')->count(),
            'unreviewed_activities' => $activities->whereNull('reviewed_by')->count(),
            'total_points_awarded' => $activities->sum('points_earned'),
            'average_word_count' => $activities->where('word_count', '>', 0)->avg('word_count') ?? 0,
            'completion_rate' => $activities->count() > 0 ? 
                round(($activities->where('is_completed', true)->count() / $activities->count()) * 100, 2) : 0,
        ];
    }
}
